<?php
/**
 * Security Enhancements
 * 
 * @package GP_Discover_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Security Class
 */
class GP_Discover_Pro_Security {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init_security_features'));
        add_action('wp_head', array($this, 'add_security_headers'));
        add_filter('wp_headers', array($this, 'add_security_headers_filter'));
        add_action('wp_enqueue_scripts', array($this, 'secure_scripts'));
        add_filter('script_loader_tag', array($this, 'add_script_security_attributes'), 10, 3);
        add_filter('style_loader_tag', array($this, 'add_style_security_attributes'), 10, 4);
        
        // Input sanitization and output escaping
        add_filter('pre_comment_content', array($this, 'sanitize_comment_content'));
        add_filter('comment_text', array($this, 'escape_comment_output'));
        
        // Prevent information disclosure
        add_filter('the_generator', '__return_empty_string');
        add_action('wp_head', array($this, 'remove_version_info'), 1);
        
        // File access protection
        add_action('init', array($this, 'protect_sensitive_files'));
        
        // Login security
        add_action('wp_login_failed', array($this, 'log_failed_login'));
        add_filter('authenticate', array($this, 'limit_login_attempts'), 30, 3);
    }
    
    /**
     * Initialize Security Features
     */
    public function init_security_features() {
        // Remove unnecessary WordPress features that could expose information
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        
        // Disable XML-RPC
        add_filter('xmlrpc_enabled', '__return_false');
        
        // Remove REST API links from head
        remove_action('wp_head', 'rest_output_link_wp_head');
        remove_action('wp_head', 'wp_oembed_add_discovery_links');
        
        // Disable file editing in admin
        if (!defined('DISALLOW_FILE_EDIT')) {
            define('DISALLOW_FILE_EDIT', true);
        }
        
        // Hide login errors
        add_filter('login_errors', array($this, 'hide_login_errors'));
        
        // Remove version from scripts and styles
        add_filter('script_loader_src', array($this, 'remove_version_from_assets'), 15, 1);
        add_filter('style_loader_src', array($this, 'remove_version_from_assets'), 15, 1);
        
        // Secure cookies
        add_action('init', array($this, 'secure_cookies'));
    }
    
    /**
     * Add Security Headers
     */
    public function add_security_headers() {
        if (!is_admin()) {
            // Content Security Policy
            $csp_directives = array(
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com *.google-analytics.com",
                "style-src 'self' 'unsafe-inline' *.googleapis.com *.gstatic.com",
                "font-src 'self' *.gstatic.com data:",
                "img-src 'self' data: *.gravatar.com *.wp.com",
                "connect-src 'self'",
                "frame-ancestors 'none'",
                "base-uri 'self'",
                "form-action 'self'"
            );
            
            $csp = implode('; ', $csp_directives);
            
            echo '<meta http-equiv="Content-Security-Policy" content="' . esc_attr($csp) . '">' . "\n";
            echo '<meta http-equiv="X-Content-Type-Options" content="nosniff">' . "\n";
            echo '<meta http-equiv="X-Frame-Options" content="DENY">' . "\n";
            echo '<meta http-equiv="X-XSS-Protection" content="1; mode=block">' . "\n";
            echo '<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">' . "\n";
            echo '<meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">' . "\n";
        }
    }
    
    /**
     * Add Security Headers Filter
     */
    public function add_security_headers_filter($headers) {
        if (!is_admin()) {
            $headers['X-Content-Type-Options'] = 'nosniff';
            $headers['X-Frame-Options'] = 'DENY';
            $headers['X-XSS-Protection'] = '1; mode=block';
            $headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';
            $headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains';
            
            // Content Security Policy
            $csp_directives = array(
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com",
                "style-src 'self' 'unsafe-inline' *.googleapis.com",
                "font-src 'self' *.gstatic.com data:",
                "img-src 'self' data: *.gravatar.com",
                "frame-ancestors 'none'"
            );
            
            $headers['Content-Security-Policy'] = implode('; ', $csp_directives);
        }
        
        return $headers;
    }
    
    /**
     * Secure Scripts
     */
    public function secure_scripts() {
        // Add nonce to inline scripts
        if (!is_admin()) {
            $nonce = wp_create_nonce('gp_discover_pro_script_nonce');
            wp_localize_script('gp-discover-pro-main', 'gpSecurity', array(
                'nonce' => $nonce,
                'ajaxUrl' => admin_url('admin-ajax.php'),
            ));
        }
    }
    
    /**
     * Add Script Security Attributes
     */
    public function add_script_security_attributes($tag, $handle, $src) {
        // Add integrity and crossorigin for external scripts
        $external_scripts = array(
            'google-analytics',
            'gtag',
        );
        
        if (in_array($handle, $external_scripts)) {
            $tag = str_replace('<script ', '<script crossorigin="anonymous" ', $tag);
        }
        
        // Add nonce to our scripts
        if (strpos($handle, 'gp-discover-pro') !== false) {
            $nonce = wp_create_nonce('gp_discover_pro_script_nonce');
            $tag = str_replace('<script ', '<script nonce="' . $nonce . '" ', $tag);
        }
        
        return $tag;
    }
    
    /**
     * Add Style Security Attributes
     */
    public function add_style_security_attributes($html, $handle, $href, $media) {
        // Add integrity for external stylesheets
        if (strpos($href, 'googleapis.com') !== false) {
            $html = str_replace('<link ', '<link crossorigin="anonymous" ', $html);
        }
        
        return $html;
    }
    
    /**
     * Sanitize Comment Content
     */
    public function sanitize_comment_content($content) {
        // Remove potentially dangerous HTML tags and attributes
        $allowed_tags = array(
            'p' => array(),
            'br' => array(),
            'strong' => array(),
            'em' => array(),
            'a' => array(
                'href' => array(),
                'title' => array(),
            ),
            'blockquote' => array(),
            'code' => array(),
        );
        
        return wp_kses($content, $allowed_tags);
    }
    
    /**
     * Escape Comment Output
     */
    public function escape_comment_output($content) {
        // Additional escaping for comment output
        return wp_kses_post($content);
    }
    
    /**
     * Remove Version Information
     */
    public function remove_version_info() {
        // Remove WordPress version from various places
        remove_action('wp_head', 'wp_generator');
        
        // Remove version from RSS feeds
        add_filter('the_generator', '__return_empty_string');
    }
    
    /**
     * Protect Sensitive Files
     */
    public function protect_sensitive_files() {
        // Create .htaccess rules to protect sensitive files
        $htaccess_rules = array(
            '# Protect sensitive files',
            '<Files "wp-config.php">',
            'Order allow,deny',
            'Deny from all',
            '</Files>',
            '',
            '<Files "error_log">',
            'Order allow,deny',
            'Deny from all',
            '</Files>',
            '',
            '<Files ".htaccess">',
            'Order allow,deny',
            'Deny from all',
            '</Files>',
            '',
            '# Prevent access to PHP files in uploads',
            '<Directory "' . wp_upload_dir()['basedir'] . '">',
            '<Files "*.php">',
            'Order allow,deny',
            'Deny from all',
            '</Files>',
            '</Directory>',
        );
        
        // This would typically be handled by the server configuration
        // but we can add it as a reference for manual implementation
    }
    
    /**
     * Hide Login Errors
     */
    public function hide_login_errors($error) {
        return __('Login failed. Please check your credentials.', 'gp-discover-pro');
    }
    
    /**
     * Remove Version from Assets
     */
    public function remove_version_from_assets($src) {
        if (strpos($src, 'ver=')) {
            $src = remove_query_arg('ver', $src);
        }
        return $src;
    }
    
    /**
     * Secure Cookies
     */
    public function secure_cookies() {
        if (is_ssl()) {
            ini_set('session.cookie_secure', 1);
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_samesite', 'Strict');
        }
    }
    
    /**
     * Log Failed Login Attempts
     */
    public function log_failed_login($username) {
        $ip = $this->get_client_ip();
        $time = current_time('mysql');
        
        // Log to WordPress error log
        error_log("Failed login attempt for user: {$username} from IP: {$ip} at {$time}");
        
        // Store in transient for rate limiting
        $attempts = get_transient('failed_login_attempts_' . $ip) ?: 0;
        $attempts++;
        set_transient('failed_login_attempts_' . $ip, $attempts, 15 * MINUTE_IN_SECONDS);
    }
    
    /**
     * Limit Login Attempts
     */
    public function limit_login_attempts($user, $username, $password) {
        $ip = $this->get_client_ip();
        $attempts = get_transient('failed_login_attempts_' . $ip) ?: 0;
        
        // Block after 5 failed attempts
        if ($attempts >= 5) {
            return new WP_Error('too_many_attempts', 
                __('Too many failed login attempts. Please try again later.', 'gp-discover-pro')
            );
        }
        
        return $user;
    }
    
    /**
     * Get Client IP Address
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, 
                        FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Sanitize Input Data
     */
    public static function sanitize_input($input, $type = 'text') {
        switch ($type) {
            case 'email':
                return sanitize_email($input);
            case 'url':
                return esc_url_raw($input);
            case 'int':
                return intval($input);
            case 'float':
                return floatval($input);
            case 'html':
                return wp_kses_post($input);
            case 'textarea':
                return sanitize_textarea_field($input);
            case 'text':
            default:
                return sanitize_text_field($input);
        }
    }
    
    /**
     * Escape Output Data
     */
    public static function escape_output($output, $context = 'html') {
        switch ($context) {
            case 'attr':
                return esc_attr($output);
            case 'url':
                return esc_url($output);
            case 'js':
                return esc_js($output);
            case 'textarea':
                return esc_textarea($output);
            case 'html':
            default:
                return esc_html($output);
        }
    }
    
    /**
     * Verify Nonce
     */
    public static function verify_nonce($nonce, $action = 'gp_discover_pro_nonce') {
        return wp_verify_nonce($nonce, $action);
    }
    
    /**
     * Check User Capabilities
     */
    public static function check_user_capability($capability = 'edit_posts') {
        return current_user_can($capability);
    }
    
    /**
     * Validate CSRF Token
     */
    public static function validate_csrf_token() {
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'gp_discover_pro_action')) {
            wp_die(__('Security check failed. Please try again.', 'gp-discover-pro'));
        }
    }
    
    /**
     * Secure File Upload
     */
    public static function secure_file_upload($file) {
        // Check file type
        $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx');
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_types)) {
            return new WP_Error('invalid_file_type', __('File type not allowed.', 'gp-discover-pro'));
        }
        
        // Check file size (5MB limit)
        if ($file['size'] > 5 * 1024 * 1024) {
            return new WP_Error('file_too_large', __('File size exceeds limit.', 'gp-discover-pro'));
        }
        
        // Scan for malicious content (basic check)
        $file_content = file_get_contents($file['tmp_name']);
        $malicious_patterns = array(
            '/<\?php/',
            '/<script/',
            '/javascript:/',
            '/vbscript:/',
            '/onload=/i',
            '/onerror=/i',
        );
        
        foreach ($malicious_patterns as $pattern) {
            if (preg_match($pattern, $file_content)) {
                return new WP_Error('malicious_file', __('File contains potentially malicious content.', 'gp-discover-pro'));
            }
        }
        
        return true;
    }
}

// Initialize Security
new GP_Discover_Pro_Security();

/**
 * Security Helper Functions
 */

/**
 * Sanitize input wrapper
 */
function gp_discover_pro_sanitize($input, $type = 'text') {
    return GP_Discover_Pro_Security::sanitize_input($input, $type);
}

/**
 * Escape output wrapper
 */
function gp_discover_pro_escape($output, $context = 'html') {
    return GP_Discover_Pro_Security::escape_output($output, $context);
}

/**
 * Verify nonce wrapper
 */
function gp_discover_pro_verify_nonce($nonce, $action = 'gp_discover_pro_nonce') {
    return GP_Discover_Pro_Security::verify_nonce($nonce, $action);
}
