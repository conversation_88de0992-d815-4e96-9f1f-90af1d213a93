<?php
/**
 * SEO Optimization
 * 
 * @package GP_Discover_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SEO Optimization Class
 */
class GP_Discover_Pro_SEO {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_head', array($this, 'add_meta_tags'), 1);
        add_action('wp_head', array($this, 'add_open_graph_tags'), 2);
        add_action('wp_head', array($this, 'add_twitter_cards'), 3);
        add_action('wp_head', array($this, 'add_schema_markup'), 4);
        add_action('wp_head', array($this, 'add_canonical_url'), 5);
        add_filter('wp_title', array($this, 'optimize_title'), 10, 3);
        add_filter('document_title_separator', array($this, 'title_separator'));
        add_action('wp_footer', array($this, 'add_structured_data'));
        add_filter('the_content', array($this, 'optimize_content_for_seo'));
        add_action('init', array($this, 'add_breadcrumbs_support'));
    }
    
    /**
     * Add Meta Tags
     */
    public function add_meta_tags() {
        global $post;
        
        // Viewport meta tag for mobile
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">' . "\n";
        
        // Theme color for mobile browsers
        echo '<meta name="theme-color" content="' . get_theme_mod('primary_color', '#ff6b35') . '">' . "\n";
        
        // Description meta tag
        $description = '';
        if (is_singular()) {
            if (has_excerpt($post->ID)) {
                $description = get_the_excerpt($post->ID);
            } else {
                $description = wp_trim_words(strip_tags($post->post_content), 25, '...');
            }
        } elseif (is_category()) {
            $description = category_description();
        } elseif (is_tag()) {
            $description = tag_description();
        } elseif (is_home() || is_front_page()) {
            $description = get_bloginfo('description');
        }
        
        if ($description) {
            echo '<meta name="description" content="' . esc_attr(wp_strip_all_tags($description)) . '">' . "\n";
        }
        
        // Keywords meta tag (if set)
        if (is_singular()) {
            $tags = get_the_tags($post->ID);
            if ($tags) {
                $keywords = array();
                foreach ($tags as $tag) {
                    $keywords[] = $tag->name;
                }
                echo '<meta name="keywords" content="' . esc_attr(implode(', ', $keywords)) . '">' . "\n";
            }
        }
        
        // Author meta tag
        if (is_singular()) {
            echo '<meta name="author" content="' . esc_attr(get_the_author_meta('display_name', $post->post_author)) . '">' . "\n";
        }
        
        // Robots meta tag
        $robots = array();
        if (is_search() || is_404()) {
            $robots[] = 'noindex';
            $robots[] = 'nofollow';
        } else {
            $robots[] = 'index';
            $robots[] = 'follow';
        }
        
        if (!empty($robots)) {
            echo '<meta name="robots" content="' . esc_attr(implode(', ', $robots)) . '">' . "\n";
        }
        
        // Language meta tag
        echo '<meta name="language" content="' . esc_attr(get_locale()) . '">' . "\n";
    }
    
    /**
     * Add Open Graph Tags
     */
    public function add_open_graph_tags() {
        if (!get_theme_mod('enable_opengraph', true)) {
            return;
        }
        
        global $post;
        
        echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="og:locale" content="' . esc_attr(get_locale()) . '">' . "\n";
        
        if (is_singular()) {
            echo '<meta property="og:type" content="article">' . "\n";
            echo '<meta property="og:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
            echo '<meta property="og:url" content="' . esc_url(get_permalink()) . '">' . "\n";
            
            $description = has_excerpt() ? get_the_excerpt() : wp_trim_words(strip_tags($post->post_content), 25, '...');
            echo '<meta property="og:description" content="' . esc_attr($description) . '">' . "\n";
            
            if (has_post_thumbnail()) {
                $image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
                echo '<meta property="og:image" content="' . esc_url($image[0]) . '">' . "\n";
                echo '<meta property="og:image:width" content="' . esc_attr($image[1]) . '">' . "\n";
                echo '<meta property="og:image:height" content="' . esc_attr($image[2]) . '">' . "\n";
            }
            
            echo '<meta property="article:published_time" content="' . esc_attr(get_the_date('c')) . '">' . "\n";
            echo '<meta property="article:modified_time" content="' . esc_attr(get_the_modified_date('c')) . '">' . "\n";
            echo '<meta property="article:author" content="' . esc_attr(get_the_author_meta('display_name')) . '">' . "\n";
            
            $categories = get_the_category();
            if ($categories) {
                foreach ($categories as $category) {
                    echo '<meta property="article:section" content="' . esc_attr($category->name) . '">' . "\n";
                }
            }
            
            $tags = get_the_tags();
            if ($tags) {
                foreach ($tags as $tag) {
                    echo '<meta property="article:tag" content="' . esc_attr($tag->name) . '">' . "\n";
                }
            }
        } else {
            echo '<meta property="og:type" content="website">' . "\n";
            echo '<meta property="og:title" content="' . esc_attr(wp_get_document_title()) . '">' . "\n";
            echo '<meta property="og:url" content="' . esc_url(home_url($_SERVER['REQUEST_URI'])) . '">' . "\n";
            echo '<meta property="og:description" content="' . esc_attr(get_bloginfo('description')) . '">' . "\n";
        }
    }
    
    /**
     * Add Twitter Cards
     */
    public function add_twitter_cards() {
        if (!get_theme_mod('enable_twitter_cards', true)) {
            return;
        }
        
        global $post;
        
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        
        $twitter_handle = get_theme_mod('social_twitter');
        if ($twitter_handle) {
            $twitter_handle = str_replace('https://twitter.com/', '@', $twitter_handle);
            echo '<meta name="twitter:site" content="' . esc_attr($twitter_handle) . '">' . "\n";
        }
        
        if (is_singular()) {
            echo '<meta name="twitter:title" content="' . esc_attr(get_the_title()) . '">' . "\n";
            
            $description = has_excerpt() ? get_the_excerpt() : wp_trim_words(strip_tags($post->post_content), 25, '...');
            echo '<meta name="twitter:description" content="' . esc_attr($description) . '">' . "\n";
            
            if (has_post_thumbnail()) {
                $image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
                echo '<meta name="twitter:image" content="' . esc_url($image[0]) . '">' . "\n";
            }
            
            $author_twitter = get_the_author_meta('twitter');
            if ($author_twitter) {
                echo '<meta name="twitter:creator" content="@' . esc_attr($author_twitter) . '">' . "\n";
            }
        }
    }
    
    /**
     * Add Schema Markup
     */
    public function add_schema_markup() {
        if (!get_theme_mod('enable_schema', true)) {
            return;
        }
        
        global $post;
        
        if (is_singular('post')) {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Article',
                'headline' => get_the_title(),
                'description' => has_excerpt() ? get_the_excerpt() : wp_trim_words(strip_tags($post->post_content), 25, '...'),
                'datePublished' => get_the_date('c'),
                'dateModified' => get_the_modified_date('c'),
                'author' => array(
                    '@type' => 'Person',
                    'name' => get_the_author_meta('display_name'),
                    'url' => get_author_posts_url(get_the_author_meta('ID'))
                ),
                'publisher' => array(
                    '@type' => 'Organization',
                    'name' => get_bloginfo('name'),
                    'url' => home_url(),
                    'logo' => array(
                        '@type' => 'ImageObject',
                        'url' => get_theme_mod('custom_logo') ? wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'full') : get_stylesheet_directory_uri() . '/assets/images/logo.png'
                    )
                ),
                'mainEntityOfPage' => array(
                    '@type' => 'WebPage',
                    '@id' => get_permalink()
                )
            );
            
            if (has_post_thumbnail()) {
                $image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
                $schema['image'] = array(
                    '@type' => 'ImageObject',
                    'url' => $image[0],
                    'width' => $image[1],
                    'height' => $image[2]
                );
            }
            
            echo '<script type="application/ld+json">' . wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
        }
        
        // Organization schema for homepage
        if (is_front_page()) {
            $organization_schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url(),
                'description' => get_bloginfo('description'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_theme_mod('custom_logo') ? wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'full') : get_stylesheet_directory_uri() . '/assets/images/logo.png'
                )
            );
            
            // Add social media profiles
            $social_profiles = array();
            $social_networks = array('facebook', 'twitter', 'instagram', 'linkedin', 'youtube');
            
            foreach ($social_networks as $network) {
                $url = get_theme_mod("social_{$network}");
                if ($url) {
                    $social_profiles[] = $url;
                }
            }
            
            if (!empty($social_profiles)) {
                $organization_schema['sameAs'] = $social_profiles;
            }
            
            echo '<script type="application/ld+json">' . wp_json_encode($organization_schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
        }
    }
    
    /**
     * Add Canonical URL
     */
    public function add_canonical_url() {
        $canonical_url = '';
        
        if (is_singular()) {
            $canonical_url = get_permalink();
        } elseif (is_category()) {
            $canonical_url = get_category_link(get_queried_object_id());
        } elseif (is_tag()) {
            $canonical_url = get_tag_link(get_queried_object_id());
        } elseif (is_author()) {
            $canonical_url = get_author_posts_url(get_queried_object_id());
        } elseif (is_home() || is_front_page()) {
            $canonical_url = home_url('/');
        }
        
        if ($canonical_url) {
            echo '<link rel="canonical" href="' . esc_url($canonical_url) . '">' . "\n";
        }
    }
    
    /**
     * Optimize Title
     */
    public function optimize_title($title, $sep, $seplocation) {
        global $paged, $page;
        
        if (is_feed()) {
            return $title;
        }
        
        // Add pagination to title
        if ($paged >= 2 || $page >= 2) {
            $title .= " {$sep} " . sprintf(__('Page %s', 'gp-discover-pro'), max($paged, $page));
        }
        
        return $title;
    }
    
    /**
     * Title Separator
     */
    public function title_separator($sep) {
        return '|';
    }
    
    /**
     * Add Structured Data
     */
    public function add_structured_data() {
        // Website schema
        $website_schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'description' => get_bloginfo('description'),
            'potentialAction' => array(
                '@type' => 'SearchAction',
                'target' => array(
                    '@type' => 'EntryPoint',
                    'urlTemplate' => home_url('/?s={search_term_string}')
                ),
                'query-input' => 'required name=search_term_string'
            )
        );
        
        echo '<script type="application/ld+json">' . wp_json_encode($website_schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
    
    /**
     * Optimize Content for SEO
     */
    public function optimize_content_for_seo($content) {
        // Add alt text to images without alt attributes
        $content = preg_replace_callback('/<img([^>]*?)>/i', function($matches) {
            $img_tag = $matches[0];
            
            // Check if alt attribute exists
            if (strpos($img_tag, 'alt=') === false) {
                // Extract src for generating alt text
                preg_match('/src=["\']([^"\']*)["\']/', $img_tag, $src_matches);
                if (isset($src_matches[1])) {
                    $filename = basename($src_matches[1]);
                    $alt_text = ucwords(str_replace(array('-', '_', '.jpg', '.jpeg', '.png', '.gif', '.webp'), array(' ', ' ', '', '', '', '', ''), $filename));
                    $img_tag = str_replace('<img', '<img alt="' . esc_attr($alt_text) . '"', $img_tag);
                }
            }
            
            return $img_tag;
        }, $content);
        
        return $content;
    }
    
    /**
     * Add Breadcrumbs Support
     */
    public function add_breadcrumbs_support() {
        add_action('gp_discover_pro_breadcrumbs', array($this, 'display_breadcrumbs'));
    }
    
    /**
     * Display Breadcrumbs
     */
    public function display_breadcrumbs() {
        if (!get_theme_mod('enable_breadcrumbs', true)) {
            return;
        }
        
        $breadcrumbs = array();
        $breadcrumbs[] = array(
            'title' => __('Home', 'gp-discover-pro'),
            'url' => home_url('/')
        );
        
        if (is_category()) {
            $category = get_queried_object();
            $breadcrumbs[] = array(
                'title' => $category->name,
                'url' => get_category_link($category->term_id)
            );
        } elseif (is_single()) {
            $categories = get_the_category();
            if ($categories) {
                $breadcrumbs[] = array(
                    'title' => $categories[0]->name,
                    'url' => get_category_link($categories[0]->term_id)
                );
            }
            $breadcrumbs[] = array(
                'title' => get_the_title(),
                'url' => get_permalink()
            );
        } elseif (is_page()) {
            $breadcrumbs[] = array(
                'title' => get_the_title(),
                'url' => get_permalink()
            );
        }
        
        if (count($breadcrumbs) > 1) {
            echo '<nav class="breadcrumbs" aria-label="' . esc_attr__('Breadcrumb Navigation', 'gp-discover-pro') . '">';
            echo '<ol class="breadcrumb-list">';
            
            foreach ($breadcrumbs as $index => $breadcrumb) {
                $is_last = ($index === count($breadcrumbs) - 1);
                echo '<li class="breadcrumb-item' . ($is_last ? ' active' : '') . '">';
                
                if (!$is_last) {
                    echo '<a href="' . esc_url($breadcrumb['url']) . '">' . esc_html($breadcrumb['title']) . '</a>';
                } else {
                    echo '<span>' . esc_html($breadcrumb['title']) . '</span>';
                }
                
                echo '</li>';
            }
            
            echo '</ol>';
            echo '</nav>';
        }
    }
}

// Initialize SEO Optimization
new GP_Discover_Pro_SEO();
