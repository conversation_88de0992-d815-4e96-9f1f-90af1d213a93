# GP Discover Theme Installation Guide

## 📋 Prerequisites
- WordPress 5.0+ installed
- GeneratePress theme (free version)
- Required plugins

## 🔧 Required Plugins
1. **GenerateBlocks** (Free) - For page building
2. **GeneratePress Premium** (Optional) - For advanced features

## 📂 Installation Steps

### Step 1: Install Base Theme
1. Go to WordPress Admin → Appearance → Themes
2. Click "Add New"
3. Search for "GeneratePress"
4. Install and Activate

### Step 2: Install Child Theme
1. Upload `gp-discover-pro` folder to `/wp-content/themes/`
2. Go to Appearance → Themes
3. Activate "GP Discover Pro"

### Step 3: Import Theme Settings
1. Go to Appearance → Customize
2. Look for "Import/Export" option
3. Import the `generatepress-settings.json` file

### Step 4: Import Elements
1. Install GenerateBlocks plugin
2. Go to GP Elements → Import
3. Upload `elements.xml` file
4. Import all elements

### Step 5: Create Home Page
1. Create new page "Home"
2. Copy content from `home-page.json`
3. Paste in block editor
4. Set as homepage in Settings → Reading

## 🎨 Customization Options
- Colors can be changed in Customizer → Global Colors
- Layout options in Customizer → Layout
- Typography in Customizer → Typography

## 📱 Mobile Optimization
- Theme is fully responsive
- Touch targets optimized for mobile
- Fast loading optimized

## 🔍 SEO Ready
- Schema markup included
- Fast loading
- Accessibility optimized

## 🆘 Troubleshooting
- Clear cache after installation
- Check plugin compatibility
- Ensure PHP 7.4+ for best performance
