/*
Theme Name: GP Discover Pro
Description: Modern WordPress theme optimized for Hindi content with accessibility features
Author: WPLiteTheme.com
Template: generatepress
Version: 2.0.0
*/

/* ===== THEME FOUNDATION ===== */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #ff6b35;
    --primary-hover: #e55a2b;
    --text-primary: #2c3e50;
    --text-secondary: #34495e;
    --text-light: #666666;
    --background: #ffffff;
    --background-light: #f8f9fa;
    --border-color: #fef2e7;
    --border-light: #e9ecef;
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
    --border-radius: 7px;
    --border-radius-large: 12px;
    --transition: all 0.3s ease;
}

/* Base Typography for Hindi Content */
body {
    font-family: 'Inter', 'Noto Sans Devanagari', -apple-system, BlinkMacSystemFont, sans-serif;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    line-height: 1.7;
    word-spacing: 0.1em;
}

/* Hindi Text Optimization */
.hindi-content,
.entry-content,
.gb-text {
    line-height: 1.7;
    word-spacing: 0.1em;
}

.entry-content p {
    margin-bottom: 1.2em;
    text-align: justify;
    hyphens: auto;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1em;
    color: var(--text-primary);
    line-height: 1.3;
}

/* ===== LAYOUT & STRUCTURE ===== */

/* Container */
.site-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.main-navigation {
    box-shadow: var(--shadow-light);
    background: var(--background);
}

.main-navigation a {
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
}

.main-navigation a:hover {
    color: var(--primary-color);
}

/* ===== CLEAN CATEGORY HEADERS ===== */

/* Clean minimal headers - no background, simple text */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    background: none !important;
    color: var(--text-primary) !important;
    padding: 0 !important;
    border: none !important;
    font-weight: 600 !important;
    font-size: 28px !important;
    text-align: left !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    box-shadow: none !important;
    margin-bottom: 25px !important;
    transition: var(--transition) !important;
    position: relative !important;
    display: block !important;
}

/* Simple underline accent */
.gb-text-4666a877:after,
.gb-text-54ea9bb4:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover {
    color: var(--primary-color) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== POST CARDS & CONTENT ===== */

/* Post Cards */
.gb-loop-item {
    background: var(--background);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.gb-loop-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Post Images */
.gb-media-97d5d870,
.gb-media-8778d61a,
.gb-media-e16007a1 {
    border-radius: var(--border-radius) !important;
    transition: var(--transition);
}

/* Post Titles */
.gb-text-d67b07a7,
.gb-text-b56eacd4,
.gb-text-6b888b19 {
    font-weight: 700 !important;
    line-height: 1.4 !important;
}

.gb-text-d67b07a7 a,
.gb-text-b56eacd4 a,
.gb-text-6b888b19 a {
    color: var(--text-primary) !important;
    text-decoration: none;
    transition: var(--transition);
}

.gb-text-d67b07a7 a:hover,
.gb-text-b56eacd4 a:hover,
.gb-text-6b888b19 a:hover {
    color: var(--primary-color) !important;
}

/* Author Info */
.gb-text-4f7117f3,
.gb-text-5beb8661,
.gb-text-57a31dfc {
    font-size: 12px !important;
    color: var(--text-light) !important;
}

/* Author Avatar */
.gb-media-7e79f4af,
.gb-media-e333169e,
.gb-media-bf31265d {
    border: 1.5px solid var(--primary-color) !important;
    border-radius: 50% !important;
}

/* Verification Badge */
.gb-shape svg {
    color: var(--primary-color) !important;
}

/* ===== PAGINATION ===== */

.gb-query-loop-pagination {
    margin-top: 30px;
    text-align: center;
}

.gb-query-loop-pagination a {
    background: var(--background) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-light) !important;
    border-radius: var(--border-radius) !important;
    padding: 12px 16px !important;
    margin: 0 4px !important;
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.gb-query-loop-pagination a:hover {
    background: var(--primary-color) !important;
    color: var(--background) !important;
    border-color: var(--primary-color) !important;
}

/* ===== SOCIAL MEDIA (THEME CONSISTENT) ===== */

.social-theme-consistent {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 25px 0;
    padding: 20px;
    background: var(--background-light);
    border-radius: var(--border-radius-large);
    border-left: 4px solid var(--primary-color);
}

.social-theme-text {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
    margin-right: 10px;
}

.social-theme-icons {
    display: flex;
    gap: 10px;
}

.social-theme-icon {
    width: 44px;
    height: 44px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    background: var(--background);
    color: var(--text-secondary);
}

.social-theme-icon:hover {
    background: var(--primary-color);
    color: var(--background);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

/* Override existing social buttons */
.wpjankari-social-sharing {
    background: var(--background) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--border-radius) !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
}

.wpjankari-social-sharing:hover {
    background: var(--primary-color) !important;
    color: var(--background) !important;
    border-color: var(--primary-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3) !important;
}

/* ===== ACCESSIBILITY ===== */

/* Skip Link */
.skip-link {
    position: absolute;
    left: -9999px;
    top: -9999px;
    background: var(--text-primary);
    color: var(--background);
    padding: 8px 16px;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
}

.skip-link:focus {
    left: 10px;
    top: 10px;
    z-index: 999999;
}

/* Screen Reader Text */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:focus {
    background: var(--background-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    clip: auto !important;
    color: var(--text-primary);
    display: block;
    font-size: 14px;
    font-weight: 600;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}

/* Focus Indicators */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Form Elements */
input[type="search"],
input[type="text"],
input[type="email"],
textarea {
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    min-height: 44px;
    font-family: inherit;
    transition: var(--transition);
}

input[type="search"]:focus,
input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
    .site-content {
        padding: 0 15px;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 24px !important;
    }
}

@media (max-width: 768px) {
    /* Typography */
    .entry-content {
        font-size: 16px;
        line-height: 1.8;
    }

    h1, h2, h3 {
        line-height: 1.4;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 22px !important;
        margin-bottom: 20px !important;
    }

    /* Touch Targets */
    .main-navigation a {
        min-height: 44px;
        display: flex;
        align-items: center;
        padding: 12px 15px;
    }

    .gb-query-loop-pagination a {
        min-height: 44px !important;
        min-width: 44px !important;
        padding: 12px 16px !important;
        margin: 4px !important;
    }

    .gb-text a {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        padding: 8px 4px;
    }

    .gb-media a {
        display: block;
        min-height: 44px;
    }

    /* Social Icons */
    .social-theme-consistent {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .social-theme-icons {
        width: 100%;
        justify-content: center;
    }
}

/* ===== UTILITY CLASSES ===== */

/* Hide elements */
.hide-headers .gb-text-4666a877,
.hide-headers .gb-text-54ea9bb4 {
    display: none !important;
}

/* Indian accent elements */
.indian-accent {
    border-left: 4px solid var(--primary-color);
    padding-left: 15px;
    background: var(--border-color);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* ===== ANIMATIONS ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gb-loop-item {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== ACCESSIBILITY MEDIA QUERIES ===== */

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .gb-text a {
        text-decoration: underline;
    }

    .social-theme-icon {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .gb-loop-item:hover {
        transform: none;
    }

    .social-theme-icon:hover {
        transform: none;
    }
}

/* ===== PRINT STYLES ===== */

@media print {
    .social-theme-consistent,
    .gb-query-loop-pagination,
    .main-navigation {
        display: none !important;
    }

    .gb-text a {
        color: #000 !important;
        text-decoration: underline;
    }
}
