/*
 Theme Name:   GP Discover Pro
 Theme URI:    https://wplitetheme.com/gp-discover-pro/
 Description:  GeneratePress Child theme for mobile responsive discovers blog and news website. Using this child theme you can easily create a google discover blog website. This GeneratePress Child theme design by wplitetheme.com
 Author:       WPLiteTheme.com
 Author URI:   https://wplitetheme.com
 Template:     generatepress
 Version:      1.4.0
*/

.main-navigation {
	box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
/* Sticky widgets */ 
.auto-width.gb-query-loop-wrapper {
    flex: 1;
}
@media (min-width: 768px) {
.sticky-container > .gb-inside-container,.sticky-container {
    position: sticky;
    top: 80px;
}
#right-sidebar .inside-right-sidebar {
    height: 100%;
}
}
select#wp-block-categories-1 {
    width: 100%;
}
.wplite-banner-ads {
	margin-bottom: 15px;
}
.rank-math-breadcrumb p {
	background: #e5e8ec;
	color: #000000;
	padding: 5px 10px;
	border-radius: 4px;
    font-size: 11px;
	font-weight: 700;
}
@media (max-width: 768px){
	.rank-math-breadcrumb p {
		margin: 0px 10px;
	}
}
/*Block Images*/
.wp-block-image {
    padding-top: 10px;
    padding-bottom: 20px;
}

.wp-block-image img {
    box-shadow: 0 10px 10px 0 rgb(0 0 0 / 6%);
    border: 1px solid #cfcfcf;
    padding: 3px;
}
/*Table of contents*/
#toc_container li a {
    display: block;
    width: 100%;
    color: var(--link-text);
    padding: 10px 1em;
    border-top: 1px solid #aaa;
}
#toc_container {
    background: #f9f9f900;
    border: 1px solid #aaa;
    padding: 0px;
}
#toc_container span.toc_toggle {
    font-weight: 400;
    background: #fff;
    padding: 3px 20px;
    font-size: 18px;
    text-transform: capitalize;
    text-align: center;
    display: block;
}
/*Floating Social Share*/
.wplitetheme-float-social-wrapper {
    position: fixed;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 9999;
}

.wplitetheme-float-social-sharing {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    align-items: flex-start;
    min-height: 40px;
    font-size: 12px;
    padding: 5px 10px;
}

.wplitetheme-float-social-sharing svg {
    position: relative;
    top: 0.5em;
}

.wplitetheme-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}

.wplitetheme-social-facebook:hover {
    background-color: rgba(59, 89, 152, .8);
}

.wplitetheme-social-twitter {
    fill: #fff;
    background-color: #0f1419;
}

.wplitetheme-social-twitter:hover {
    background-color: #0f1419;
}

.wplitetheme-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}

.wplitetheme-social-whatsapp:hover {
    background-color: rgba(37, 211, 102, .8);
}

/*Social Share*/
.wpjankari-social-wrapper {
    margin: 20px 5px 20px 5px;
    font-size: 0;
    text-align: center;
	display: flex;
    flex-wrap: wrap;
}
.wpjankari-social-sharing {
	padding: 8px;
	margin: 4px;
	border-radius: 3px;
    flex: 1;
    transition: background-color 0.3s, transform 0.3s, color 0.3s;
}
.wpjankari-social-sharing:hover {
	transform: translateY(-3px);
	border: none;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
}
@media only screen and (max-width: 600px) {
    .wpjankari-social-sharing {
		display: inline-block;
    }
}
.wpjankari-social-sharing svg {
    position: relative;
    top: 0.15em;
    display: inline-block;
}
.wpjankari-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}
.wpjankari-social-twitter {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
.wpjankari-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}
.wpjankari-social-telegram {
    fill: #fff;
    background-color: rgb(2, 126, 189);
}
.wpjankari-social-more {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
/*Follow Us Button*/
.whatsapphighlight {
	animation: WPLiteTheme-GreenBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-GreenBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #25d366;
	}
	100% {
		border-color: transparent;
	}
}
.telegramhighlight {
	animation: WPLiteTheme-BlueBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-BlueBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #0086ce;
	}
	100% {
		border-color: transparent;
	}
}
/*Tag Cloud*/
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a {
    font-size: 12px!important;
    margin-bottom: 8px;
    margin-right: 8px;
}
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a,  div.tags a{
    color: #333333;
    display: inline-block;
    padding: 8px 15px; /* Increased from 4px to 8px for better touch targets */
    line-height: 1.2;
    margin-right: 10px;
    background: #fff;
    border: 1px solid #333333;
    border-radius: 25px;
    min-height: 44px; /* Minimum touch target size */
    min-width: 44px;  /* Minimum touch target size */
    box-sizing: border-box;
}

/* Accessibility Improvements */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}

.skip-link {
    left: -9999px;
    position: absolute;
    top: -9999px;
}

.skip-link:focus {
    left: 6px;
    top: 7px;
}

/* Improve touch targets for mobile */
@media (max-width: 768px) {
    /* Navigation menu items */
    .main-navigation a {
        min-height: 44px;
        display: flex;
        align-items: center;
        padding: 12px 15px;
    }

    /* Social sharing buttons */
    .wpjankari-social-sharing {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px; /* Increased padding */
        margin: 6px; /* Increased margin for better spacing */
    }

    /* Pagination buttons */
    .gb-query-loop-pagination a {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        margin: 4px;
    }

    /* Author links and post titles */
    .gb-text a {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        padding: 8px 4px;
    }

    /* Image links */
    .gb-media a {
        display: block;
        min-height: 44px;
    }
}

/* Focus indicators for better keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #005fcc;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gb-text a {
        text-decoration: underline;
    }

    .wpjankari-social-sharing {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .wpjankari-social-sharing {
        transition: none;
    }

    .wpjankari-social-sharing:hover {
        transform: none;
    }

    .whatsapphighlight,
    .telegramhighlight {
        animation: none;
    }
}

/* Color contrast improvements */
.gb-text a {
    color: #0066cc; /* Better contrast ratio */
}

.gb-text a:hover {
    color: #004499; /* Darker on hover for better contrast */
}

/* Ensure sufficient color contrast for text */
.gb-text-d31b2236,
.gb-text-48340570,
.gb-text-c97b5596 {
    color: #333333; /* Darker text for better contrast */
}

/* Improve form accessibility */
input[type="search"],
input[type="text"],
input[type="email"],
textarea {
    border: 2px solid #ccc;
    padding: 8px 12px;
    min-height: 44px;
}

input[type="search"]:focus,
input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
    border-color: #005fcc;
    box-shadow: 0 0 0 2px rgba(0, 95, 204, 0.2);
}

/* Hindi Content Optimizations */
/* Better typography for Devanagari script */
body {
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
}

/* Hindi text specific improvements */
.hindi-content,
.entry-content,
.gb-text {
    line-height: 1.7; /* Better line spacing for Hindi */
    word-spacing: 0.1em; /* Improved word spacing */
}

/* Hindi headings */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600; /* Better weight for Hindi headings */
    margin-bottom: 1em;
}

/* Improved readability for Hindi content */
.entry-content p {
    margin-bottom: 1.2em;
    text-align: justify;
    hyphens: auto;
}

/* Better contrast for Hindi text */
.gb-text-d67b07a7 a,
.gb-text-b56eacd4 a,
.gb-text-6b888b19 a {
    color: #d35400; /* Saffron-based link color */
}

.gb-text-d67b07a7 a:hover,
.gb-text-b56eacd4 a:hover,
.gb-text-6b888b19 a:hover {
    color: #a04000; /* Darker saffron on hover */
}

/* Category headers with Indian theme */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    background: linear-gradient(135deg, #ff6b35 0%, #f39c12 100%);
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Author verification badge - Indian style */
.gb-shape svg {
    color: #ff6b35 !important; /* Saffron verification badge */
}

/* Social sharing - Indian colors */
.wpjankari-social-whatsapp {
    background-color: #25d366; /* WhatsApp green */
}

.wpjankari-social-telegram {
    background-color: #0088cc; /* Telegram blue */
}

/* Custom Indian-themed elements */
.indian-accent {
    border-left: 4px solid #ff6b35;
    padding-left: 15px;
    background: #fef2e7;
}

/* Better mobile experience for Hindi */
@media (max-width: 768px) {
    .entry-content {
        font-size: 16px; /* Larger text for mobile Hindi reading */
        line-height: 1.8;
    }

    h1, h2, h3 {
        line-height: 1.4;
    }
}