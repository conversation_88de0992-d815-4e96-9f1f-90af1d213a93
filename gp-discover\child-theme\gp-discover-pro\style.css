/*
Theme Name: GP Discover Pro
Description: Modern responsive WordPress theme optimized for Hindi content
Author: WPLiteTheme.com
Template: generatepress
Version: 3.0.0
*/

/* ===== MODERN THEME FOUNDATION ===== */

/* CSS Variables - Complete Color System */
:root {
    /* Primary Colors */
    --primary-color: #ff6b35;
    --primary-hover: #e55a2b;
    --primary-light: #ff8c5a;
    --primary-dark: #d4541c;

    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #34495e;
    --text-light: #7f8c8d;
    --text-muted: #95a5a6;
    --text-white: #ffffff;

    /* Background Colors */
    --background: #ffffff;
    --background-light: #f8f9fa;
    --background-dark: #2c3e50;
    --background-accent: #fef2e7;
    --background-card: #ffffff;

    /* Border Colors */
    --border-color: #e9ecef;
    --border-light: #f1f3f4;
    --border-accent: #fef2e7;
    --border-dark: #dee2e6;

    /* Shadow System */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.1);

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;

    /* Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Typography */
    --font-primary: 'Inter', 'Noto Sans Devanagari', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-secondary: 'Noto Sans Devanagari', serif;

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Container Widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ===== MODERN TYPOGRAPHY SYSTEM ===== */

/* Base Typography */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
    background-color: var(--background);
    margin: 0;
    padding: 0;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    word-spacing: 0.1em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Modern Heading System */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin: 0 0 var(--space-lg) 0;
    letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* Responsive Typography */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
}

/* Text Elements */
p {
    margin: 0 0 var(--space-lg) 0;
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Hindi Content Optimization */
.hindi-content,
.entry-content,
.gb-text {
    line-height: 1.8;
    word-spacing: 0.1em;
    font-family: var(--font-secondary);
}

.entry-content p {
    margin-bottom: var(--space-lg);
    text-align: justify;
    hyphens: auto;
}

/* ===== MODERN LAYOUT SYSTEM ===== */

/* Container System */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
@media (max-width: 1024px) {
    .grid-cols-4 { grid-template-columns: repeat(2, 1fr); }
    .grid-cols-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 { grid-template-columns: 1fr; }
}

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Spacing Utilities */
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }
.gap-2xl { gap: var(--space-2xl); }

/* ===== MODERN HEADER & NAVIGATION ===== */

/* Site Header */
.site-header {
    background: var(--background);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.site-branding {
    padding: var(--space-lg) 0;
}

.site-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.025em;
}

.site-description {
    color: var(--text-light);
    font-size: 0.875rem;
    margin: var(--space-xs) 0 0 0;
    font-weight: 400;
}

/* Modern Navigation */
.main-navigation {
    background: var(--background);
    border-top: 1px solid var(--border-light);
    box-shadow: none;
}

.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.main-navigation li {
    margin: 0;
}

.main-navigation a {
    display: block;
    padding: var(--space-lg) var(--space-xl);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: var(--transition-fast);
    border-radius: var(--radius-md);
    position: relative;
}

.main-navigation a:hover,
.main-navigation a:focus {
    color: var(--primary-color);
    background: var(--background-accent);
    text-decoration: none;
}

.main-navigation a.current {
    color: var(--primary-color);
    background: var(--background-accent);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .main-navigation ul {
        flex-direction: column;
        gap: 0;
    }

    .main-navigation a {
        padding: var(--space-md) var(--space-lg);
        text-align: center;
        border-radius: 0;
        border-bottom: 1px solid var(--border-light);
    }

    .main-navigation a:last-child {
        border-bottom: none;
    }
}

/* ===== MODERN CATEGORY HEADERS ===== */

/* Clean Modern Category Headers */
.gb-text-4666a877,
.gb-text-54ea9bb4,
h2.gb-text-4666a877,
h2.gb-text-54ea9bb4,
.gb-text.gb-text-4666a877,
.gb-text.gb-text-54ea9bb4 {
    /* Reset all fancy styling */
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    text-shadow: none !important;
    padding: 0 !important;

    /* Modern typography */
    color: var(--text-primary) !important;
    font-family: var(--font-primary) !important;
    font-size: 1.875rem !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
    letter-spacing: -0.025em !important;
    text-align: left !important;
    text-transform: none !important;

    /* Modern spacing */
    margin: 0 0 var(--space-2xl) 0 !important;

    /* Clean display */
    display: block !important;
    position: relative !important;
    overflow: visible !important;

    /* Remove animations */
    transition: var(--transition-fast) !important;
    animation: none !important;
    transform: none !important;
}

/* Modern underline accent */
.gb-text-4666a877:after,
.gb-text-54ea9bb4:after {
    content: '' !important;
    position: absolute !important;
    bottom: -8px !important;
    left: 0 !important;
    width: 60px !important;
    height: 4px !important;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light)) !important;
    border-radius: var(--radius-sm) !important;
    display: block !important;
}

/* Modern hover effect */
.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover {
    color: var(--primary-color) !important;
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

/* Remove any other pseudo-elements */
.gb-text-4666a877:before,
.gb-text-54ea9bb4:before {
    display: none !important;
    content: none !important;
}

/* ===== MODERN POST CARDS & CONTENT ===== */

/* Modern Post Grid Layout */
.gb-looper-b95cf960,
.gb-looper-a0414682,
.gb-looper-f79b4eb4 {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
    gap: var(--space-2xl) !important;
    margin: var(--space-2xl) 0 !important;
}

/* Modern Post Cards */
.gb-loop-item {
    background: var(--background-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    position: relative;
}

.gb-loop-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-accent);
}

/* Modern Post Images */
.gb-media-97d5d870,
.gb-media-8778d61a,
.gb-media-e16007a1 {
    width: 100% !important;
    height: 200px !important;
    object-fit: cover !important;
    border-radius: 0 !important;
    transition: var(--transition-normal) !important;
    margin-bottom: var(--space-md) !important;
}

.gb-loop-item:hover .gb-media-97d5d870,
.gb-loop-item:hover .gb-media-8778d61a,
.gb-loop-item:hover .gb-media-e16007a1 {
    transform: scale(1.05);
}

/* Post Content Container */
.post-content {
    padding: var(--space-lg);
}

/* Modern Post Titles */
.gb-text-d67b07a7,
.gb-text-b56eacd4,
.gb-text-6b888b19 {
    font-weight: 700 !important;
    line-height: 1.3 !important;
    margin-bottom: var(--space-lg) !important;
    font-size: 1.25rem !important;
}

.gb-text-d67b07a7 a,
.gb-text-b56eacd4 a,
.gb-text-6b888b19 a {
    color: var(--text-primary) !important;
    text-decoration: none !important;
    transition: var(--transition-fast) !important;
    display: block !important;
}

.gb-text-d67b07a7 a:hover,
.gb-text-b56eacd4 a:hover,
.gb-text-6b888b19 a:hover {
    color: var(--primary-color) !important;
}

/* Modern Author Section */
.gb-element-6f73e5ba,
.gb-element-bcff044a,
.gb-element-5417cc07 {
    display: flex !important;
    align-items: center !important;
    gap: var(--space-sm) !important;
    padding: var(--space-md) 0 !important;
    border-top: 1px solid var(--border-light) !important;
    margin-top: var(--space-lg) !important;
}

/* Modern Author Info */
.gb-text-4f7117f3,
.gb-text-5beb8661,
.gb-text-57a31dfc {
    font-size: 0.875rem !important;
    color: var(--text-light) !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    gap: var(--space-xs) !important;
}

/* Modern Author Avatar */
.gb-media-7e79f4af,
.gb-media-e333169e,
.gb-media-bf31265d {
    width: 32px !important;
    height: 32px !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: var(--radius-full) !important;
    object-fit: cover !important;
}

/* Modern Verification Badge */
.gb-shape svg {
    color: var(--primary-color) !important;
    width: 16px !important;
    height: 16px !important;
}

/* Date and Separator */
.gb-text-fbe00f43,
.gb-text-23d24cd8,
.gb-text-42bb1d6f,
.gb-text-48340570,
.gb-text-c97b5596,
.gb-text-97c2b24d {
    font-size: 0.875rem !important;
    color: var(--text-muted) !important;
    font-weight: 400 !important;
}

/* Responsive Post Cards */
@media (max-width: 768px) {
    .gb-looper-b95cf960,
    .gb-looper-a0414682,
    .gb-looper-f79b4eb4 {
        grid-template-columns: 1fr !important;
        gap: var(--space-xl) !important;
    }

    .gb-media-97d5d870,
    .gb-media-8778d61a,
    .gb-media-e16007a1 {
        height: 180px !important;
    }

    .post-content {
        padding: var(--space-md);
    }

    .gb-text-d67b07a7,
    .gb-text-b56eacd4,
    .gb-text-6b888b19 {
        font-size: 1.125rem !important;
    }
}

/* ===== MODERN PAGINATION ===== */

.gb-query-loop-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin: var(--space-2xl) 0;
    padding: var(--space-xl) 0;
}

.gb-query-loop-pagination a,
.gb-query-loop-pagination .page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-sm) var(--space-md);
    background: var(--background-card);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.gb-query-loop-pagination a:hover,
.gb-query-loop-pagination .page-numbers:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.gb-query-loop-pagination .page-numbers.current {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.gb-query-loop-pagination .page-numbers.dots {
    background: transparent;
    border: none;
    box-shadow: none;
    color: var(--text-muted);
}

/* Previous/Next buttons */
.gb-text-5f6cdbaa,
.gb-text-6dc778f6 {
    display: inline-flex !important;
    align-items: center !important;
    gap: var(--space-xs) !important;
    padding: var(--space-md) var(--space-lg) !important;
    background: var(--background-card) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius-md) !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    transition: var(--transition-fast) !important;
    box-shadow: var(--shadow-sm) !important;
}

.gb-text-5f6cdbaa:hover,
.gb-text-6dc778f6:hover {
    background: var(--primary-color) !important;
    color: var(--text-white) !important;
    border-color: var(--primary-color) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
}

/* Mobile Pagination */
@media (max-width: 768px) {
    .gb-query-loop-pagination {
        flex-wrap: wrap;
        gap: var(--space-xs);
    }

    .gb-query-loop-pagination a,
    .gb-query-loop-pagination .page-numbers {
        min-width: 40px;
        min-height: 40px;
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.8rem;
    }

    .gb-text-5f6cdbaa,
    .gb-text-6dc778f6 {
        padding: var(--space-sm) var(--space-md) !important;
        font-size: 0.875rem !important;
    }
}

/* ===== MODERN SOCIAL MEDIA SECTION ===== */

.social-section {
    margin: var(--space-2xl) 0;
    padding: var(--space-2xl) 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
    background: var(--background-light);
}

.social-theme-consistent {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xl);
    padding: var(--space-2xl);
    background: var(--background-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.social-theme-consistent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.social-theme-text {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.125rem;
    margin-right: var(--space-lg);
    white-space: nowrap;
}

.social-theme-icons {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
}

.social-theme-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    transition: var(--transition-normal);
    border: 2px solid var(--border-color);
    background: var(--background);
    color: var(--text-secondary);
    position: relative;
    overflow: hidden;
}

.social-theme-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.social-theme-icon:hover::before {
    left: 100%;
}

.social-theme-icon:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

/* Individual Social Platform Colors */
.social-facebook-theme:hover {
    background: #1877f2;
    border-color: #1877f2;
}

.social-whatsapp-theme:hover {
    background: #25d366;
    border-color: #25d366;
}

.social-telegram-theme:hover {
    background: #0088cc;
    border-color: #0088cc;
}

.social-share-theme:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Mobile Social Media */
@media (max-width: 768px) {
    .social-theme-consistent {
        flex-direction: column;
        gap: var(--space-lg);
        padding: var(--space-xl);
    }

    .social-theme-text {
        margin-right: 0;
        margin-bottom: var(--space-sm);
        text-align: center;
    }

    .social-theme-icons {
        justify-content: center;
        gap: var(--space-lg);
    }

    .social-theme-icon {
        width: 56px;
        height: 56px;
        font-size: 1.5rem;
    }
}

/* ===== MODERN FOOTER ===== */

.site-footer {
    background: var(--background-dark);
    color: var(--text-white);
    padding: var(--space-2xl) 0 var(--space-xl) 0;
    margin-top: var(--space-2xl);
    border-top: 4px solid var(--primary-color);
}

.footer-content {
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.footer-content p {
    margin: 0;
    color: var(--text-white);
    font-size: 0.875rem;
    opacity: 0.9;
}

.footer-navigation {
    display: flex;
    gap: var(--space-xl);
    flex-wrap: wrap;
}

.footer-navigation a {
    color: var(--text-white);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
    transition: var(--transition-fast);
}

.footer-navigation a:hover {
    opacity: 1;
    color: var(--primary-color);
    text-decoration: underline;
}

/* Mobile Footer */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .footer-navigation {
        justify-content: center;
        gap: var(--space-lg);
    }
}

/* Override existing social buttons */
.wpjankari-social-sharing {
    background: var(--background-card) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius-md) !important;
    font-weight: 500 !important;
    transition: var(--transition-normal) !important;
    min-height: 44px;
    min-width: 44px;
    padding: var(--space-md) var(--space-lg);
    box-shadow: var(--shadow-sm);
}

.wpjankari-social-sharing:hover {
    background: var(--primary-color) !important;
    color: var(--text-white) !important;
    border-color: var(--primary-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* ===== MODERN ACCESSIBILITY ===== */

/* Skip Link */
.skip-link {
    position: absolute;
    left: -9999px;
    top: -9999px;
    background: var(--background-dark);
    color: var(--text-white);
    padding: var(--space-md) var(--space-lg);
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    z-index: 999999;
    box-shadow: var(--shadow-xl);
}

.skip-link:focus {
    left: var(--space-lg);
    top: var(--space-lg);
}

/* Screen Reader Text */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
}

.screen-reader-text:focus {
    background: var(--background-card);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    clip: auto !important;
    color: var(--text-primary);
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    height: auto;
    left: var(--space-md);
    line-height: normal;
    padding: var(--space-md) var(--space-lg);
    text-decoration: none;
    top: var(--space-md);
    width: auto;
    z-index: 100000;
    border: 2px solid var(--primary-color);
}

/* Modern Focus Indicators */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus,
.social-theme-icon:focus,
.gb-query-loop-pagination a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

/* Modern Form Elements */
input[type="search"],
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md) var(--space-lg);
    min-height: 44px;
    font-family: var(--font-primary);
    font-size: 1rem;
    color: var(--text-primary);
    background: var(--background-card);
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

input[type="search"]:focus,
input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1), var(--shadow-md);
    outline: none;
}

/* Modern Button Styles */
button,
.button,
input[type="submit"],
input[type="button"] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    min-height: 44px;
    background: var(--primary-color);
    color: var(--text-white);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

button:hover,
.button:hover,
input[type="submit"]:hover,
input[type="button"]:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

button:active,
.button:active,
input[type="submit"]:active,
input[type="button"]:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* ===== MODERN RESPONSIVE DESIGN ===== */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    .container {
        max-width: var(--container-2xl);
    }

    .gb-looper-b95cf960,
    .gb-looper-a0414682,
    .gb-looper-f79b4eb4 {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

/* Desktop (1024px - 1439px) */
@media (max-width: 1439px) and (min-width: 1024px) {
    .container {
        max-width: var(--container-xl);
        padding: 0 var(--space-xl);
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 1.75rem !important;
    }
}

/* Tablet (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
    .container {
        max-width: var(--container-lg);
        padding: 0 var(--space-lg);
    }

    .site-header {
        padding: var(--space-md) 0;
    }

    .site-title {
        font-size: 1.75rem;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 1.625rem !important;
        margin-bottom: var(--space-xl) !important;
    }

    .gb-looper-b95cf960,
    .gb-looper-a0414682,
    .gb-looper-f79b4eb4 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: var(--space-xl) !important;
    }
}

/* Mobile Large (480px - 767px) */
@media (max-width: 767px) and (min-width: 480px) {
    .container {
        padding: 0 var(--space-md);
    }

    .site-header {
        padding: var(--space-md) 0;
    }

    .site-title {
        font-size: 1.5rem;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 1.5rem !important;
        margin-bottom: var(--space-lg) !important;
    }

    .gb-looper-b95cf960,
    .gb-looper-a0414682,
    .gb-looper-f79b4eb4 {
        grid-template-columns: 1fr !important;
        gap: var(--space-lg) !important;
    }

    /* Touch-friendly navigation */
    .main-navigation a {
        min-height: 48px;
        padding: var(--space-md) var(--space-lg);
        font-size: 1rem;
    }

    /* Larger touch targets */
    .gb-text a {
        min-height: 48px;
        display: inline-flex;
        align-items: center;
        padding: var(--space-sm);
    }

    .gb-media a {
        display: block;
        min-height: 48px;
    }
}

/* Mobile Small (< 480px) */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--space-sm);
    }

    .site-header {
        padding: var(--space-sm) 0;
    }

    .site-title {
        font-size: 1.25rem;
    }

    .site-description {
        font-size: 0.8rem;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        font-size: 1.375rem !important;
        margin-bottom: var(--space-lg) !important;
    }

    .gb-looper-b95cf960,
    .gb-looper-a0414682,
    .gb-looper-f79b4eb4 {
        grid-template-columns: 1fr !important;
        gap: var(--space-md) !important;
    }

    .post-content {
        padding: var(--space-sm);
    }

    .gb-text-d67b07a7,
    .gb-text-b56eacd4,
    .gb-text-6b888b19 {
        font-size: 1.125rem !important;
    }

    /* Extra large touch targets for small screens */
    .main-navigation a {
        min-height: 52px;
        padding: var(--space-lg);
        font-size: 1.125rem;
    }

    .social-theme-icon {
        width: 52px !important;
        height: 52px !important;
        font-size: 1.375rem !important;
    }

    .gb-query-loop-pagination a,
    .gb-query-loop-pagination .page-numbers {
        min-width: 48px;
        min-height: 48px;
        padding: var(--space-md);
    }
}

/* ===== MODERN UTILITY CLASSES ===== */

/* Display Utilities */
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* Position Utilities */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

/* Text Utilities */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }

.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }
.text-2xl { font-size: 1.5rem !important; }
.text-3xl { font-size: 1.875rem !important; }

/* Color Utilities */
.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-light { color: var(--text-light) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-white { color: var(--text-white) !important; }
.text-accent { color: var(--primary-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-white { background-color: var(--background) !important; }
.bg-light { background-color: var(--background-light) !important; }
.bg-dark { background-color: var(--background-dark) !important; }
.bg-accent { background-color: var(--background-accent) !important; }

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.m-auto { margin: auto !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }

/* Border Utilities */
.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: none !important; }
.border-t { border-top: 1px solid var(--border-color) !important; }
.border-b { border-bottom: 1px solid var(--border-color) !important; }
.border-l { border-left: 1px solid var(--border-color) !important; }
.border-r { border-right: 1px solid var(--border-color) !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-full { border-radius: var(--radius-full) !important; }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-none { box-shadow: none !important; }

/* Special Elements */
.indian-accent {
    border-left: 4px solid var(--primary-color);
    padding-left: var(--space-lg);
    background: var(--background-accent);
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.highlight-box {
    background: var(--background-accent);
    border: 1px solid var(--border-accent);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    margin: var(--space-xl) 0;
}

.card {
    background: var(--background-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--space-xl);
    transition: var(--transition-normal);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Hide elements */
.hide-headers .gb-text-4666a877,
.hide-headers .gb-text-54ea9bb4 {
    display: none !important;
}

/* Responsive Utilities */
@media (max-width: 767px) {
    .hidden-mobile { display: none !important; }
    .block-mobile { display: block !important; }
    .text-center-mobile { text-align: center !important; }
}

@media (min-width: 768px) {
    .hidden-desktop { display: none !important; }
    .block-desktop { display: block !important; }
}

/* ===== MODERN ANIMATIONS ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Apply animations to elements */
.gb-loop-item {
    animation: fadeInUp 0.6s ease-out;
}

.site-header {
    animation: slideInDown 0.8s ease-out;
}

.social-theme-consistent {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* ===== ACCESSIBILITY & USER PREFERENCES ===== */

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000000;
        --text-primary: #000000;
        --text-secondary: #000000;
        --background: #ffffff;
        --border-color: #000000;
    }

    .gb-text a,
    a {
        text-decoration: underline !important;
        color: #000000 !important;
    }

    .social-theme-icon,
    button,
    .button {
        border: 3px solid #000000 !important;
        background: #ffffff !important;
        color: #000000 !important;
    }

    .gb-loop-item {
        border: 2px solid #000000 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .gb-loop-item:hover,
    .social-theme-icon:hover,
    .card:hover,
    button:hover,
    .button:hover {
        transform: none !important;
    }

    .social-theme-icon::before {
        display: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --background: #1a1a1a;
        --background-light: #2d2d2d;
        --background-card: #2d2d2d;
        --background-dark: #000000;
        --text-primary: #ffffff;
        --text-secondary: #e0e0e0;
        --text-light: #b0b0b0;
        --text-muted: #888888;
        --border-color: #404040;
        --border-light: #333333;
        --border-accent: #ff6b35;
    }

    .gb-loop-item {
        background: var(--background-card);
        border-color: var(--border-color);
    }

    .site-header {
        background: var(--background);
        border-color: var(--border-color);
    }

    .main-navigation a {
        color: var(--text-secondary);
    }

    .main-navigation a:hover {
        background: var(--background-light);
    }
}

/* Print Styles */
@media print {
    .social-theme-consistent,
    .gb-query-loop-pagination,
    .main-navigation,
    .site-header,
    .site-footer {
        display: none !important;
    }

    .gb-loop-item {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000000 !important;
        margin-bottom: 1rem !important;
    }

    .gb-text a {
        color: #000000 !important;
        text-decoration: underline !important;
    }

    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000000 !important;
        background: #ffffff !important;
    }

    .gb-text-4666a877,
    .gb-text-54ea9bb4 {
        color: #000000 !important;
        font-size: 16pt !important;
        margin-bottom: 0.5rem !important;
    }

    .gb-text-4666a877:after,
    .gb-text-54ea9bb4:after {
        display: none !important;
    }
}

/* ===== ADDITIONAL OVERRIDES ===== */

/* Override any GenerateBlocks inline styles */
[class*="gb-text-4666a877"],
[class*="gb-text-54ea9bb4"] {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin-bottom: 20px !important;
    color: #333333 !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    text-align: left !important;
    text-transform: none !important;
}

/* Override any theme or plugin styles */
.gb-element .gb-text-4666a877,
.gb-element .gb-text-54ea9bb4,
.gb-container .gb-text-4666a877,
.gb-container .gb-text-54ea9bb4 {
    background: transparent !important;
    background-color: transparent !important;
    color: #333333 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

/* Force override any CSS custom properties */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    --gb-background-color: transparent !important;
    --gb-text-color: #333333 !important;
    --gb-padding-top: 0 !important;
    --gb-padding-bottom: 0 !important;
    --gb-padding-left: 0 !important;
    --gb-padding-right: 0 !important;
    --gb-margin-bottom: 20px !important;
    --gb-border-radius: 0 !important;
    --gb-box-shadow: none !important;
}

/* ===== PRINT STYLES ===== */

@media print {
    .social-theme-consistent,
    .gb-query-loop-pagination,
    .main-navigation {
        display: none !important;
    }

    .gb-text a {
        color: #000 !important;
        text-decoration: underline;
    }
}
