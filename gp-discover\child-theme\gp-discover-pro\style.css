/*
 Theme Name:   GP Discover Pro
 Theme URI:    https://wplitetheme.com/gp-discover-pro/
 Description:  GeneratePress Child theme for mobile responsive discovers blog and news website. Using this child theme you can easily create a google discover blog website. This GeneratePress Child theme design by wplitetheme.com
 Author:       WPLiteTheme.com
 Author URI:   https://wplitetheme.com
 Template:     generatepress
 Version:      1.4.0
*/

.main-navigation {
	box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
/* Sticky widgets */ 
.auto-width.gb-query-loop-wrapper {
    flex: 1;
}
@media (min-width: 768px) {
.sticky-container > .gb-inside-container,.sticky-container {
    position: sticky;
    top: 80px;
}
#right-sidebar .inside-right-sidebar {
    height: 100%;
}
}
select#wp-block-categories-1 {
    width: 100%;
}
.wplite-banner-ads {
	margin-bottom: 15px;
}
.rank-math-breadcrumb p {
	background: #e5e8ec;
	color: #000000;
	padding: 5px 10px;
	border-radius: 4px;
    font-size: 11px;
	font-weight: 700;
}
@media (max-width: 768px){
	.rank-math-breadcrumb p {
		margin: 0px 10px;
	}
}
/*Block Images*/
.wp-block-image {
    padding-top: 10px;
    padding-bottom: 20px;
}

.wp-block-image img {
    box-shadow: 0 10px 10px 0 rgb(0 0 0 / 6%);
    border: 1px solid #cfcfcf;
    padding: 3px;
}
/*Table of contents*/
#toc_container li a {
    display: block;
    width: 100%;
    color: var(--link-text);
    padding: 10px 1em;
    border-top: 1px solid #aaa;
}
#toc_container {
    background: #f9f9f900;
    border: 1px solid #aaa;
    padding: 0px;
}
#toc_container span.toc_toggle {
    font-weight: 400;
    background: #fff;
    padding: 3px 20px;
    font-size: 18px;
    text-transform: capitalize;
    text-align: center;
    display: block;
}
/*Floating Social Share*/
.wplitetheme-float-social-wrapper {
    position: fixed;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 9999;
}

.wplitetheme-float-social-sharing {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    align-items: flex-start;
    min-height: 40px;
    font-size: 12px;
    padding: 5px 10px;
}

.wplitetheme-float-social-sharing svg {
    position: relative;
    top: 0.5em;
}

.wplitetheme-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}

.wplitetheme-social-facebook:hover {
    background-color: rgba(59, 89, 152, .8);
}

.wplitetheme-social-twitter {
    fill: #fff;
    background-color: #0f1419;
}

.wplitetheme-social-twitter:hover {
    background-color: #0f1419;
}

.wplitetheme-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}

.wplitetheme-social-whatsapp:hover {
    background-color: rgba(37, 211, 102, .8);
}

/*Social Share*/
.wpjankari-social-wrapper {
    margin: 20px 5px 20px 5px;
    font-size: 0;
    text-align: center;
	display: flex;
    flex-wrap: wrap;
}
.wpjankari-social-sharing {
	padding: 8px;
	margin: 4px;
	border-radius: 3px;
    flex: 1;
    transition: background-color 0.3s, transform 0.3s, color 0.3s;
}
.wpjankari-social-sharing:hover {
	transform: translateY(-3px);
	border: none;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px;
}
@media only screen and (max-width: 600px) {
    .wpjankari-social-sharing {
		display: inline-block;
    }
}
.wpjankari-social-sharing svg {
    position: relative;
    top: 0.15em;
    display: inline-block;
}
.wpjankari-social-facebook {
    fill: #fff;
    background-color: rgba(59, 89, 152, 1);
}
.wpjankari-social-twitter {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
.wpjankari-social-whatsapp {
    fill: #fff;
    background-color: rgba(37, 211, 102, 1);
}
.wpjankari-social-telegram {
    fill: #fff;
    background-color: rgb(2, 126, 189);
}
.wpjankari-social-more {
    fill: #fff;
    background-color: rgba(0, 0, 0);
}
/*Follow Us Button*/
.whatsapphighlight {
	animation: WPLiteTheme-GreenBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-GreenBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #25d366;
	}
	100% {
		border-color: transparent;
	}
}
.telegramhighlight {
	animation: WPLiteTheme-BlueBorderAnimation 1s infinite;
}
@keyframes WPLiteTheme-BlueBorderAnimation {
	0% {
		border-color: transparent;
	}
	50% {
		border-color: #0086ce;
	}
	100% {
		border-color: transparent;
	}
}
/*Tag Cloud*/
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a {
    font-size: 12px!important;
    margin-bottom: 8px;
    margin-right: 8px;
}
.widget_epcl_tag_cloud a, .widget_tag_cloud a, .wp-block-tag-cloud a,  div.tags a{
    color: #333333;
    display: inline-block;
    padding: 8px 15px; /* Increased from 4px to 8px for better touch targets */
    line-height: 1.2;
    margin-right: 10px;
    background: #fff;
    border: 1px solid #333333;
    border-radius: 25px;
    min-height: 44px; /* Minimum touch target size */
    min-width: 44px;  /* Minimum touch target size */
    box-sizing: border-box;
}

/* Accessibility Improvements */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000;
}

.skip-link {
    left: -9999px;
    position: absolute;
    top: -9999px;
}

.skip-link:focus {
    left: 6px;
    top: 7px;
}

/* Improve touch targets for mobile */
@media (max-width: 768px) {
    /* Navigation menu items */
    .main-navigation a {
        min-height: 44px;
        display: flex;
        align-items: center;
        padding: 12px 15px;
    }

    /* Social sharing buttons */
    .wpjankari-social-sharing {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px; /* Increased padding */
        margin: 6px; /* Increased margin for better spacing */
    }

    /* Pagination buttons */
    .gb-query-loop-pagination a {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        margin: 4px;
    }

    /* Author links and post titles */
    .gb-text a {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        padding: 8px 4px;
    }

    /* Image links */
    .gb-media a {
        display: block;
        min-height: 44px;
    }
}

/* Focus indicators for better keyboard navigation */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #005fcc;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gb-text a {
        text-decoration: underline;
    }

    .wpjankari-social-sharing {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .wpjankari-social-sharing {
        transition: none;
    }

    .wpjankari-social-sharing:hover {
        transform: none;
    }

    .whatsapphighlight,
    .telegramhighlight {
        animation: none;
    }
}

/* Color contrast improvements */
.gb-text a {
    color: #0066cc; /* Better contrast ratio */
}

.gb-text a:hover {
    color: #004499; /* Darker on hover for better contrast */
}

/* Ensure sufficient color contrast for text */
.gb-text-d31b2236,
.gb-text-48340570,
.gb-text-c97b5596 {
    color: #333333; /* Darker text for better contrast */
}

/* Improve form accessibility */
input[type="search"],
input[type="text"],
input[type="email"],
textarea {
    border: 2px solid #ccc;
    padding: 8px 12px;
    min-height: 44px;
}

input[type="search"]:focus,
input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
    border-color: #005fcc;
    box-shadow: 0 0 0 2px rgba(0, 95, 204, 0.2);
}

/* Hindi Content Optimizations */
/* Better typography for Devanagari script */
body {
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
}

/* Hindi text specific improvements */
.hindi-content,
.entry-content,
.gb-text {
    line-height: 1.7; /* Better line spacing for Hindi */
    word-spacing: 0.1em; /* Improved word spacing */
}

/* Hindi headings */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600; /* Better weight for Hindi headings */
    margin-bottom: 1em;
}

/* Improved readability for Hindi content */
.entry-content p {
    margin-bottom: 1.2em;
    text-align: justify;
    hyphens: auto;
}

/* Better contrast for Hindi text */
.gb-text-d67b07a7 a,
.gb-text-b56eacd4 a,
.gb-text-6b888b19 a {
    color: #d35400; /* Saffron-based link color */
}

.gb-text-d67b07a7 a:hover,
.gb-text-b56eacd4 a:hover,
.gb-text-6b888b19 a:hover {
    color: #a04000; /* Darker saffron on hover */
}

/* Modern Category Headers */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    background: linear-gradient(135deg, #ff6b35 0%, #f39c12 50%, #e74c3c 100%);
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
    border: none;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.gb-text-4666a877:before,
.gb-text-54ea9bb4:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.gb-text-4666a877:hover:before,
.gb-text-54ea9bb4:hover:before {
    left: 100%;
}

.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 107, 53, 0.4);
}

/* Author verification badge - Indian style */
.gb-shape svg {
    color: #ff6b35 !important; /* Saffron verification badge */
}

/* Social sharing - Indian colors */
.wpjankari-social-whatsapp {
    background-color: #25d366; /* WhatsApp green */
}

.wpjankari-social-telegram {
    background-color: #0088cc; /* Telegram blue */
}

/* Custom Indian-themed elements */
.indian-accent {
    border-left: 4px solid #ff6b35;
    padding-left: 15px;
    background: #fef2e7;
}

/* Better mobile experience for Hindi */
@media (max-width: 768px) {
    .entry-content {
        font-size: 16px; /* Larger text for mobile Hindi reading */
        line-height: 1.8;
    }

    h1, h2, h3 {
        line-height: 1.4;
    }
}

/* Alternative Category Header Styles */

/* Style 1: Badge with Icon */
.category-header-badge {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(45deg, #ff6b35, #f39c12);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 18px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    position: relative;
    margin-bottom: 30px;
}

.category-header-badge:before {
    content: "📰";
    margin-right: 8px;
    font-size: 20px;
}

/* Style 2: Underline Effect */
.category-header-underline {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
    width: 100%;
}

.category-header-underline:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, #ff6b35, #f39c12);
    border-radius: 2px;
}

/* Style 3: 3D Effect */
.category-header-3d {
    background: linear-gradient(145deg, #ff6b35, #e55a2b);
    color: white;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 20px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow:
        0 8px 16px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.category-header-3d:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 24px rgba(255, 107, 53, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Style 4: Ribbon Style */
.category-header-ribbon {
    position: relative;
    background: #ff6b35;
    color: white;
    padding: 15px 40px;
    margin: 20px 0 30px 0;
    font-weight: 700;
    font-size: 18px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.category-header-ribbon:before,
.category-header-ribbon:after {
    content: '';
    position: absolute;
    top: 0;
    width: 0;
    height: 0;
    border-style: solid;
}

.category-header-ribbon:before {
    left: -20px;
    border-width: 25px 20px 25px 0;
    border-color: transparent #ff6b35 transparent transparent;
}

.category-header-ribbon:after {
    right: -20px;
    border-width: 25px 0 25px 20px;
    border-color: transparent transparent transparent #ff6b35;
}

/* Style 5: Neon Glow */
.category-header-neon {
    background: #1a1a1a;
    color: #ff6b35;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 20px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 30px;
    border: 2px solid #ff6b35;
    box-shadow:
        0 0 10px #ff6b35,
        inset 0 0 10px rgba(255, 107, 53, 0.1);
    animation: neonPulse 2s infinite alternate;
}

@keyframes neonPulse {
    from {
        box-shadow:
            0 0 10px #ff6b35,
            inset 0 0 10px rgba(255, 107, 53, 0.1);
    }
    to {
        box-shadow:
            0 0 20px #ff6b35,
            0 0 30px #ff6b35,
            inset 0 0 20px rgba(255, 107, 53, 0.2);
    }
}

/* Clean & Minimal Header Styles */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    /* Simple clean style */
    background: none !important;
    color: #2c3e50 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-weight: 700 !important;
    font-size: 28px !important;
    text-align: left !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    box-shadow: none !important;
    border: none !important;
    margin-bottom: 25px !important;
    transition: none !important;
    position: relative !important;
    overflow: visible !important;
    display: inline-block !important;
}

/* Simple underline effect */
.gb-text-4666a877:after,
.gb-text-54ea9bb4:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #ff6b35;
    border-radius: 2px;
}

.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover {
    color: #ff6b35 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Alternative Minimal Styles */

/* Style 1: Just Text (Ultra Minimal) */
.minimal-text {
    background: none !important;
    color: #333 !important;
    padding: 0 !important;
    border: none !important;
    font-weight: 600 !important;
    font-size: 24px !important;
    text-align: left !important;
    margin-bottom: 20px !important;
    text-transform: capitalize !important;
}

/* Style 2: Simple Border */
.minimal-border {
    background: none !important;
    color: #2c3e50 !important;
    padding: 10px 0 !important;
    border: none !important;
    border-bottom: 2px solid #ff6b35 !important;
    font-weight: 600 !important;
    font-size: 22px !important;
    text-align: left !important;
    margin-bottom: 25px !important;
    display: inline-block !important;
    text-transform: capitalize !important;
}

/* Style 3: Dot Accent */
.minimal-dot {
    background: none !important;
    color: #2c3e50 !important;
    padding: 0 !important;
    border: none !important;
    font-weight: 600 !important;
    font-size: 24px !important;
    text-align: left !important;
    margin-bottom: 20px !important;
    position: relative !important;
    padding-left: 15px !important;
    text-transform: capitalize !important;
}

.minimal-dot:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: #ff6b35;
    border-radius: 50%;
}

/* Style 4: Clean Typography */
.minimal-typo {
    background: none !important;
    color: #1a1a1a !important;
    padding: 0 !important;
    border: none !important;
    font-weight: 300 !important;
    font-size: 32px !important;
    text-align: left !important;
    margin-bottom: 30px !important;
    letter-spacing: -0.5px !important;
    line-height: 1.2 !important;
    text-transform: none !important;
}

/* Apply ultra minimal by default */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    /* Override with ultra minimal */
    background: none !important;
    color: #333 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-weight: 600 !important;
    font-size: 24px !important;
    text-align: left !important;
    text-transform: capitalize !important;
    letter-spacing: 0 !important;
    box-shadow: none !important;
    border: none !important;
    margin-bottom: 20px !important;
    transition: none !important;
    position: relative !important;
    overflow: visible !important;
    display: block !important;
}

.gb-text-4666a877:after,
.gb-text-54ea9bb4:after {
    display: none !important;
}

.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover {
    color: #ff6b35 !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Option: Hide headers completely */
.hide-headers .gb-text-4666a877,
.hide-headers .gb-text-54ea9bb4 {
    display: none !important;
}

/* Option: Make headers invisible but keep space */
.invisible-headers .gb-text-4666a877,
.invisible-headers .gb-text-54ea9bb4 {
    opacity: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Modern Social Media Icons */
.social-follow-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #ff6b35;
}

.social-follow-text {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
    margin-right: 10px;
}

.social-icons-modern {
    display: flex;
    gap: 12px;
}

.social-icon-modern {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.social-icon-modern:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.social-icon-modern:hover:before {
    left: 100%;
}

.social-icon-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Individual Social Colors */
.social-facebook {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
    color: white;
}

.social-whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
}

.social-telegram {
    background: linear-gradient(135deg, #0088cc, #005f8a);
    color: white;
}

.social-share {
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
}

.social-twitter {
    background: linear-gradient(135deg, #1da1f2, #0d8bd9);
    color: white;
}

.social-instagram {
    background: linear-gradient(135deg, #e4405f, #833ab4, #fcb045);
    color: white;
}

.social-youtube {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    color: white;
}

/* Alternative: Minimal Social Icons */
.social-minimal {
    display: flex;
    align-items: center;
    gap: 20px;
    margin: 15px 0;
}

.social-minimal-text {
    font-weight: 500;
    color: #666;
    font-size: 14px;
}

.social-minimal-icons {
    display: flex;
    gap: 15px;
}

.social-minimal-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #666;
    transition: all 0.3s ease;
    text-decoration: none;
    border: 2px solid transparent;
}

.social-minimal-icon:hover {
    background: white;
    border-color: #ff6b35;
    color: #ff6b35;
    transform: scale(1.1);
}

/* Alternative: Text Only Social */
.social-text-only {
    display: flex;
    align-items: center;
    gap: 25px;
    margin: 20px 0;
    font-size: 14px;
}

.social-text-link {
    color: #666;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.social-text-link:hover {
    color: #ff6b35;
}

.social-text-link:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: #ff6b35;
    transition: width 0.3s ease;
}

.social-text-link:hover:after {
    width: 100%;
}

/* Alternative: Floating Social Bar */
.social-floating {
    position: fixed;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
}

.social-floating-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.social-floating-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .social-floating {
        display: none;
    }
}