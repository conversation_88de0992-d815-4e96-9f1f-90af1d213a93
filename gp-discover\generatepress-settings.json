{"modules": {"Blog": "generate_package_blog", "Menu Plus": "generate_package_menu_plus", "Spacing": "generate_package_spacing"}, "mods": [], "options": {"generate_settings": {"navigation_background_color": "var(--background)", "nav_alignment_setting": "right", "navigation_background_hover_color": "var(--background)", "navigation_text_hover_color": "var(--color-background-hover)", "background_color": "var(--background)", "slideout_background_color": "var(--background)", "form_button_background_color": "var(--color-background)", "form_button_background_color_hover": "var(--color-background-hover)", "content_title_color": "", "blog_post_title_color": "var(--text-1)", "blog_post_title_hover_color": "var(--text-1)", "font_body": "Open Sans", "heading_1_weight": "600", "heading_2_weight": "600", "entry_meta_text_color": "var(--contrast-3)", "entry_meta_link_color": "var(--contrast-2)", "body_font_size": 17, "link_color": "var(--link-text)", "entry_meta_link_color_hover": "var(--accent-hover)", "footer_background_color": "", "footer_widget_background_color": "var(--contrast-2)", "footer_widget_text_color": "var(--base-3)", "footer_widget_link_color": "var(--base-3)", "footer_widget_title_color": "var(--base-2)", "footer_text_color": "var(--contrast-2)", "footer_link_color": "var(--contrast-2)", "footer_link_hover_color": "var(--accent-hover)", "footer_widget_setting": "0", "widget_content_font_size": 17, "sidebar_widget_background_color": "#ffffff", "sidebar_widget_text_color": "", "buttons_font_transform": "none", "buttons_font_size": 15, "widget_title_font_weight": "600", "navigation_background_current_color": "var(--background)", "nav_search": "disable", "footer_bar_alignment": "right", "site_title_color": "var(--text-2)", "site_title_font_size": 25, "mobile_site_title_font_size": 20, "combine_css": true, "icons": "svg", "structure": "flexbox", "container_width": 1200, "nav_position_setting": "nav-below-header", "container_alignment": "text", "text_color": "var(--text-1)", "header_text_color": "var(--text-2)", "header_link_color": "#3a3a3a", "navigation_text_color": "var(--text-1)", "navigation_text_current_color": "var(--text-1)", "subnavigation_background_color": "", "subnavigation_background_hover_color": "", "subnavigation_background_current_color": "", "subnavigation_text_current_color": "", "sidebar_widget_title_color": "#000000", "heading_1_font_size": "40", "mobile_heading_1_font_size": "30", "heading_2_font_size": "30", "mobile_heading_2_font_size": "25", "heading_3_font_size": "20", "mobile_heading_3_font_size": "", "heading_4_font_size": "", "mobile_heading_4_font_size": "", "heading_5_font_size": "", "mobile_heading_5_font_size": "", "underline_links": "never", "use_dynamic_typography": true, "link_color_hover": "var(--text-2)", "header_background_color": "#ffffff", "site_tagline_color": "#757575", "subnavigation_text_color": "", "subnavigation_text_hover_color": "", "content_background_color": "var(--background-1)", "form_background_color": "var(--background-1)", "form_text_color": "var(--text-2)", "form_background_color_focus": "var(--background-1)", "form_text_color_focus": "var(--text-2)", "form_border_color": "var(--text-2)", "form_border_color_focus": "var(--text-2)", "font_manager": [{"fontFamily": "Lexend Deca", "googleFont": true, "googleFontApi": 1, "googleFontCategory": "sans-serif", "googleFontVariants": "100, 200, 300, regular, 500, 600, 700, 800, 900"}], "typography": [{"selector": "body", "fontFamily": "Lexend Deca", "fontWeight": "400", "fontSize": "14px", "fontSizeMobile": "14px", "fontSizeUnit": "", "lineHeight": "", "module": "core", "group": "base"}, {"selector": "main-title", "fontSize": "18px", "fontSizeUnit": "", "module": "core", "group": "header"}, {"selector": "widget-titles", "fontWeight": "600", "module": "core", "group": "widgets"}, {"selector": "buttons", "fontSize": "15px", "fontSizeUnit": "", "module": "core", "group": "content"}, {"selector": "h1", "fontWeight": "700", "fontSize": "28px", "fontSizeTablet": "26px", "fontSizeMobile": "24px", "fontSizeUnit": "", "module": "core", "group": "content"}, {"selector": "h2", "fontFamily": "", "fontWeight": "600", "fontSize": "22px", "fontSizeTablet": "24px", "fontSizeMobile": "20px", "fontSizeUnit": "", "module": "core", "group": "content"}, {"selector": "h3", "fontWeight": "600", "fontSize": "18px", "fontSizeMobile": "18px", "fontSizeUnit": "", "module": "core", "group": "content"}, {"selector": "primary-menu-items", "customSelector": "", "fontFamily": "", "fontWeight": "500", "textTransform": "", "textDecoration": "", "fontStyle": "", "fontSize": "15px", "fontSizeTablet": "", "fontSizeMobile": "", "fontSizeUnit": "", "lineHeight": "", "lineHeightTablet": "", "lineHeightMobile": "", "lineHeightUnit": "", "letterSpacing": "", "letterSpacingTablet": "", "letterSpacingMobile": "", "letterSpacingUnit": "", "marginBottom": "", "marginBottomTablet": "", "marginBottomMobile": "", "marginBottomUnit": "", "module": "core", "group": "primaryNavigation"}], "global_colors": [{"name": "background", "slug": "background", "color": "#ffffff"}, {"name": "background-1", "slug": "background-1", "color": "#ffffff"}, {"name": "text-1", "slug": "text-1", "color": "#2c3e50"}, {"name": "text-2", "slug": "text-2", "color": "#34495e"}, {"name": "link-text", "slug": "link-text", "color": "#3498db"}, {"name": "color-background", "slug": "color-background", "color": "#2980b9"}, {"name": "color-background-hover", "slug": "color-background-hover", "color": "#1f618d"}, {"name": "border-color", "slug": "border-color", "color": "#ecf0f1"}], "footer_widget_link_hover_color": "var(--base-3)", "content_link_color": "var(--link-text)", "content_link_hover_color": "var(--link-text)", "sidebar_widget_link_color": "var(--contrast)", "sidebar_widget_link_hover_color": "var(--accent-hover)", "h1_color": "var(--text-1)", "h2_color": "var(--text-1)", "h3_color": "var(--text-1)", "dynamic_css_cache": true, "hide_title": false, "hide_tagline": true, "retina_logo": "", "content_layout_setting": "separate-containers", "layout_setting": "right-sidebar", "blog_layout_setting": "right-sidebar", "nav_layout_setting": "fluid-nav", "nav_dropdown_type": "hover", "navigation_search_background_color": "var(--base)", "navigation_search_text_color": "var(--contrast)", "smooth_scroll": false, "nav_search_modal": true, "slideout_background_hover_color": "rgba(0,0,0,0)", "slideout_background_current_color": "rgba(0,0,0,0)", "slideout_text_color": "var(--text-1)", "slideout_submenu_background_color": "rgba(0,0,0,0)", "slideout_submenu_background_hover_color": "rgba(0,0,0,0)", "slideout_submenu_background_current_color": "rgba(0,0,0,0)", "slideout_submenu_text_color": "var(--text-1)", "nav_dropdown_direction": "right", "form_button_text_color": "#ffffff", "form_button_text_color_hover": "#ffffff", "content_text_color": "var(--text-2)", "search_modal_bg_color": "#ffffff", "search_modal_text_color": "#171717", "search_modal_overlay_bg_color": "rgba(33,33,33,0.15)", "css_print_method": "file", "single_layout_setting": "right-sidebar", "slideout_text_hover_color": "var(--color-background-hover)", "nav_inner_width": "contained"}, "generate_blog_settings": {"masonry": false, "post_image": true, "date": false, "author": false, "categories": false, "tags": false, "comments": false, "excerpt_length": 20, "read_more": "Read more", "masonry_load_more": "+ More", "masonry_loading": "Loading...", "post_image_position": "", "post_image_alignment": "post-image-aligned-left", "post_image_width": 320, "post_image_height": "", "column_layout": false, "columns": "50", "featured_column": false, "single_date": true, "single_author": true, "single_categories": true, "single_tags": true, "read_more_button": true, "post_image_size": "medium_large", "page_post_image": false, "single_post_navigation": false, "single_post_image_position": "below-title", "infinite_scroll": false, "infinite_scroll_button": false, "single_post_image_padding": true, "single_post_image_size": "full"}, "generate_spacing_settings": {"right_sidebar_width": 30, "menu_item": 20, "left_sidebar_width": "25", "top_bar_right": "10", "top_bar_left": "10", "mobile_top_bar_right": "", "mobile_top_bar_left": "", "header_top": "40", "header_bottom": "40", "mobile_header_right": "", "mobile_header_left": "", "mobile_widget_top": "", "mobile_widget_right": "", "mobile_widget_bottom": "", "mobile_widget_left": "", "mobile_footer_widget_container_top": "", "mobile_footer_widget_container_right": "", "mobile_footer_widget_container_bottom": "", "mobile_footer_widget_container_left": "", "footer_right": "20", "footer_left": "20", "mobile_footer_right": "10", "mobile_footer_left": "10", "menu_item_height": 55, "content_top": 20, "content_right": 30, "content_bottom": 20, "content_left": 30, "widget_top": 15, "widget_right": 15, "widget_bottom": 15, "widget_left": 15, "separator": 25, "mobile_content_right": 20, "mobile_content_bottom": 20, "mobile_content_left": 20, "mobile_content_top": 20, "content_element_separator": 2, "off_canvas_menu_item_height": 50}, "generate_menu_plus_settings": {"sticky_menu_logo": "", "sticky_menu_logo_position": "menu", "sticky_menu": "false", "sticky_menu_effect": "none", "auto_hide_sticky": false, "navigation_as_header": true, "slideout_menu": "mobile", "slideout_close_button": "outside", "slideout_menu_style": "slide", "slideout_menu_side": "left", "mobile_header": "disable", "mobile_header_sticky": "disable", "off_canvas_desktop_toggle_label": "", "mobile_menu_label": ""}}}