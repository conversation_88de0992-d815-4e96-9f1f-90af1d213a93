<?php
/**
 * GP Discover Pro - GeneratePress Child Theme
 *
 * A clean, modern WordPress theme optimized for Hindi content
 * with accessibility features and professional design.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function gp_discover_setup() {
    // Add theme support
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));
    add_theme_support('title-tag');

    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'gp_discover_setup');

/**
 * Enqueue Styles and Scripts
 */
function gp_discover_scripts() {
    // Parent theme style
    wp_enqueue_style('generatepress-style', get_template_directory_uri() . '/style.css');

    // Child theme style
    wp_enqueue_style('gp-discover-style',
        get_stylesheet_directory_uri() . '/style.css',
        array('generatepress-style'),
        wp_get_theme()->get('Version')
    );

    // Google Fonts for Hindi content
    wp_enqueue_style('gp-discover-fonts',
        'https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap',
        array(),
        null
    );
}
add_action('wp_enqueue_scripts', 'gp_discover_scripts');

/**
 * Search Title Shortcode
 */
function gp_discover_search_title() {
    if (is_search()) {
        return '<h1 class="search-title">Search results for: <span class="search-query">' . get_search_query() . '</span></h1>';
    } elseif (is_archive()) {
        return '<h1 class="archive-title">' . get_the_archive_title() . '</h1>';
    }
    return '';
}
add_shortcode('search_title', 'gp_discover_search_title');

/**
 * Accessibility Improvements
 */
function gp_discover_accessibility_script() {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add aria-labels to image links
        document.querySelectorAll('a img').forEach(function(img) {
            const link = img.parentElement;
            if (!link.getAttribute('aria-label')) {
                const alt = img.getAttribute('alt');
                link.setAttribute('aria-label', alt || 'Read more');
            }
        });

        // Add aria-labels to pagination
        document.querySelectorAll('.gb-query-loop-pagination a').forEach(function(link) {
            const text = link.textContent.trim();
            if (text === 'Previous') {
                link.setAttribute('aria-label', 'Previous page');
            } else if (text === 'Next') {
                link.setAttribute('aria-label', 'Next page');
            } else if (!isNaN(text)) {
                link.setAttribute('aria-label', 'Page ' + text);
            }
        });

        // Add aria-labels to social icons
        document.querySelectorAll('.social-icon').forEach(function(icon) {
            if (!icon.getAttribute('aria-label')) {
                const platform = icon.className.match(/social-(\w+)/);
                if (platform) {
                    icon.setAttribute('aria-label', 'Follow us on ' + platform[1]);
                }
            }
        });
    });
    </script>
    <?php
}
add_action('wp_footer', 'gp_discover_accessibility_script');

/**
 * Add Skip Link
 */
function gp_discover_skip_link() {
    echo '<a class="skip-link screen-reader-text" href="#main">Skip to main content</a>';
}
add_action('wp_body_open', 'gp_discover_skip_link');

/**
 * Custom Body Classes
 */
function gp_discover_body_classes($classes) {
    // Add Hindi content class
    if (is_rtl()) {
        $classes[] = 'rtl-content';
    }

    // Add layout classes
    if (is_home() || is_front_page()) {
        $classes[] = 'home-layout';
    }

    return $classes;
}
add_filter('body_class', 'gp_discover_body_classes');