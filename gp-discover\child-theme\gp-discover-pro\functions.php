<?php
/**
 * GeneratePress child theme functions and definitions.
 *
 * Add your custom PHP in this file.
 * Only edit this file if you have direct access to it on your server (to fix errors if they happen).
 */

add_shortcode('search_title', 'get_search_title');
function get_search_title() {
    if (is_search()) {
        return '<h1 class="search-for">Search results for:</h1><h1 class="search-title">' . get_search_query() . '</h1>';
    } elseif (is_archive()) {
        return '<h1 class="search-title">' . get_the_archive_title() . '</h1>';
    }
}

/**
 * Accessibility Improvements
 */

// Add proper aria-labels to links without text
function gp_discover_accessibility_improvements() {
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fix links without discernible names
        const links = document.querySelectorAll('a');
        links.forEach(function(link) {
            // If link has no text content and no aria-label
            if (!link.textContent.trim() && !link.getAttribute('aria-label')) {
                // Check if it's an image link
                const img = link.querySelector('img');
                if (img) {
                    const altText = img.getAttribute('alt');
                    if (altText) {
                        link.setAttribute('aria-label', altText);
                    } else {
                        link.setAttribute('aria-label', 'Read more about this post');
                    }
                }
                // Check if it's an icon link
                else if (link.querySelector('svg') || link.querySelector('.gb-shape')) {
                    const parentText = link.closest('.gb-text')?.textContent?.trim();
                    if (parentText) {
                        link.setAttribute('aria-label', parentText);
                    } else {
                        link.setAttribute('aria-label', 'Navigation link');
                    }
                }
                // Generic fallback
                else if (!link.textContent.trim()) {
                    link.setAttribute('aria-label', 'Link');
                }
            }
        });

        // Ensure pagination links have proper labels
        const paginationLinks = document.querySelectorAll('.gb-query-loop-pagination a');
        paginationLinks.forEach(function(link) {
            if (!link.getAttribute('aria-label')) {
                const text = link.textContent.trim();
                if (text === 'Previous') {
                    link.setAttribute('aria-label', 'Go to previous page');
                } else if (text === 'Next') {
                    link.setAttribute('aria-label', 'Go to next page');
                } else if (!isNaN(text)) {
                    link.setAttribute('aria-label', 'Go to page ' + text);
                }
            }
        });
    });
    </script>
    <?php
}
add_action('wp_footer', 'gp_discover_accessibility_improvements');

// Add skip to content link
function gp_discover_skip_link() {
    echo '<a class="screen-reader-text skip-link" href="#main">Skip to content</a>';
}
add_action('wp_body_open', 'gp_discover_skip_link');

// Improve social sharing accessibility
function gp_discover_social_sharing_accessibility($content) {
    // Add proper aria-labels to social sharing buttons
    $content = str_replace(
        array(
            'class="wpjankari-social-facebook"',
            'class="wpjankari-social-twitter"',
            'class="wpjankari-social-whatsapp"',
            'class="wpjankari-social-telegram"'
        ),
        array(
            'class="wpjankari-social-facebook" aria-label="Share on Facebook"',
            'class="wpjankari-social-twitter" aria-label="Share on Twitter"',
            'class="wpjankari-social-whatsapp" aria-label="Share on WhatsApp"',
            'class="wpjankari-social-telegram" aria-label="Share on Telegram"'
        ),
        $content
    );
    return $content;
}
add_filter('the_content', 'gp_discover_social_sharing_accessibility');