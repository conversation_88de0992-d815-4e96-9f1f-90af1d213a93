<?php
/**
 * GP Discover Pro Child Theme Functions
 * 
 * Professional GeneratePress child theme with performance optimization,
 * SEO features, accessibility, and modern functionality.
 * 
 * @package GP_Discover_Pro
 * @version 1.0.0
 * <AUTHOR> WordPress Developer
 * @license GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Constants
 */
define('GP_DISCOVER_PRO_VERSION', '1.0.0');
define('GP_DISCOVER_PRO_PATH', get_stylesheet_directory());
define('GP_DISCOVER_PRO_URL', get_stylesheet_directory_uri());

/**
 * Theme Setup
 */
class GP_Discover_Pro_Theme {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('after_setup_theme', array($this, 'theme_setup'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_styles'));
        add_action('init', array($this, 'init_theme'));
        add_action('wp_head', array($this, 'add_critical_css'), 1);
        add_action('wp_head', array($this, 'add_preload_resources'), 2);
        add_action('wp_head', array($this, 'add_security_headers'), 3);
        add_action('wp_footer', array($this, 'add_performance_scripts'), 99);
        
        // Performance optimizations
        add_action('init', array($this, 'optimize_performance'));
        
        // SEO enhancements
        add_action('wp_head', array($this, 'add_seo_meta_tags'));
        add_action('wp_head', array($this, 'add_schema_markup'));
        
        // Accessibility improvements
        add_action('wp_footer', array($this, 'add_accessibility_scripts'));
        
        // Security enhancements
        add_filter('wp_headers', array($this, 'add_security_headers_filter'));
        
        // Image optimization
        add_filter('wp_get_attachment_image_attributes', array($this, 'add_lazy_loading'));
        add_filter('the_content', array($this, 'add_lazy_loading_to_content'));
        
        // Customizer
        add_action('customize_register', array($this, 'customize_register'));
    }
    
    /**
     * Theme Setup
     */
    public function theme_setup() {
        // Add theme support
        add_theme_support('html5', array(
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'style',
            'script'
        ));
        
        add_theme_support('post-thumbnails');
        add_theme_support('responsive-embeds');
        add_theme_support('wp-block-styles');
        add_theme_support('align-wide');
        add_theme_support('custom-logo');
        add_theme_support('custom-header');
        add_theme_support('custom-background');
        
        // Add image sizes
        add_image_size('gp-discover-featured', 800, 400, true);
        add_image_size('gp-discover-thumbnail', 300, 200, true);
        add_image_size('gp-discover-large', 1200, 600, true);
        
        // Load text domain
        load_child_theme_textdomain('gp-discover-pro', GP_DISCOVER_PRO_PATH . '/languages');
        
        // Add editor styles
        add_theme_support('editor-styles');
        add_editor_style('assets/css/editor-style.css');
        
        // Navigation menus
        register_nav_menus(array(
            'primary' => esc_html__('Primary Menu', 'gp-discover-pro'),
            'footer' => esc_html__('Footer Menu', 'gp-discover-pro'),
            'mobile' => esc_html__('Mobile Menu', 'gp-discover-pro'),
        ));
    }
    
    /**
     * Initialize Theme
     */
    public function init_theme() {
        // Remove unnecessary WordPress features for performance
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');
        
        // Disable emojis for performance
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
        remove_action('admin_print_scripts', 'print_emoji_detection_script');
        remove_action('admin_print_styles', 'print_emoji_styles');
        
        // Clean up WordPress head
        remove_action('wp_head', 'feed_links_extra', 3);
        remove_action('wp_head', 'feed_links', 2);
        
        // Add security headers
        if (!is_admin()) {
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');
            header('Referrer-Policy: strict-origin-when-cross-origin');
        }
    }
    
    /**
     * Enqueue Styles
     */
    public function enqueue_styles() {
        // Parent theme style
        wp_enqueue_style(
            'generatepress-style',
            get_template_directory_uri() . '/style.css',
            array(),
            wp_get_theme()->get('Version')
        );
        
        // Child theme style
        wp_enqueue_style(
            'gp-discover-pro-style',
            GP_DISCOVER_PRO_URL . '/style.css',
            array('generatepress-style'),
            GP_DISCOVER_PRO_VERSION
        );
        
        // Google Fonts with font-display: swap for performance
        wp_enqueue_style(
            'gp-discover-pro-fonts',
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+Devanagari:wght@300;400;500;600;700;800&display=swap',
            array(),
            null
        );
        
        // Non-critical CSS (loaded asynchronously)
        wp_enqueue_style(
            'gp-discover-pro-non-critical',
            GP_DISCOVER_PRO_URL . '/assets/css/non-critical.css',
            array('gp-discover-pro-style'),
            GP_DISCOVER_PRO_VERSION,
            'all'
        );
        
        // Add preload for critical fonts
        add_action('wp_head', function() {
            echo '<link rel="preload" href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin>';
        }, 1);
    }
    
    /**
     * Enqueue Scripts
     */
    public function enqueue_scripts() {
        // Main theme script
        wp_enqueue_script(
            'gp-discover-pro-main',
            GP_DISCOVER_PRO_URL . '/assets/js/main.min.js',
            array('jquery'),
            GP_DISCOVER_PRO_VERSION,
            true
        );
        
        // Lazy loading script
        wp_enqueue_script(
            'gp-discover-pro-lazy',
            GP_DISCOVER_PRO_URL . '/assets/js/lazy-loading.min.js',
            array(),
            GP_DISCOVER_PRO_VERSION,
            true
        );
        
        // Performance monitoring
        wp_enqueue_script(
            'gp-discover-pro-performance',
            GP_DISCOVER_PRO_URL . '/assets/js/performance.min.js',
            array(),
            GP_DISCOVER_PRO_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('gp-discover-pro-main', 'gpDiscoverPro', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gp_discover_pro_nonce'),
            'isRTL' => is_rtl(),
            'isMobile' => wp_is_mobile(),
        ));
    }
    
    /**
     * Add Critical CSS inline
     */
    public function add_critical_css() {
        // Critical CSS is already in style.css
        // This function can be used to add page-specific critical CSS
        if (is_front_page()) {
            echo '<style id="critical-homepage-css">';
            echo '.hero-section { background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary)); }';
            echo '</style>';
        }
    }
    
    /**
     * Preload Critical Resources
     */
    public function add_preload_resources() {
        // Preload critical CSS
        echo '<link rel="preload" href="' . GP_DISCOVER_PRO_URL . '/style.css" as="style">';
        
        // Preload critical JavaScript
        echo '<link rel="preload" href="' . GP_DISCOVER_PRO_URL . '/assets/js/main.min.js" as="script">';
        
        // Preload hero image on homepage
        if (is_front_page()) {
            $hero_image = get_theme_mod('hero_background_image');
            if ($hero_image) {
                echo '<link rel="preload" href="' . esc_url($hero_image) . '" as="image">';
            }
        }
        
        // DNS prefetch for external resources
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
        echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">';
    }
    
    /**
     * Add Security Headers
     */
    public function add_security_headers() {
        echo '<meta http-equiv="X-Content-Type-Options" content="nosniff">';
        echo '<meta http-equiv="X-Frame-Options" content="SAMEORIGIN">';
        echo '<meta http-equiv="X-XSS-Protection" content="1; mode=block">';
        echo '<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">';
        
        // Content Security Policy (basic)
        $csp = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; style-src 'self' 'unsafe-inline' *.googleapis.com; font-src 'self' *.gstatic.com; img-src 'self' data: *.gravatar.com;";
        echo '<meta http-equiv="Content-Security-Policy" content="' . $csp . '">';
    }
}

// Initialize the theme
new GP_Discover_Pro_Theme();

/**
 * Utility Functions
 */

/**
 * Sanitize and escape output
 */
function gp_discover_pro_sanitize_output($output) {
    return wp_kses_post($output);
}

/**
 * Get theme option with default
 */
function gp_discover_pro_get_option($option, $default = '') {
    return get_theme_mod($option, $default);
}

/**
 * Check if we're on mobile
 */
function gp_discover_pro_is_mobile() {
    return wp_is_mobile();
}

/**
 * Get optimized image
 */
function gp_discover_pro_get_optimized_image($attachment_id, $size = 'full', $lazy = true) {
    $image = wp_get_attachment_image($attachment_id, $size, false, array(
        'loading' => $lazy ? 'lazy' : 'eager',
        'decoding' => 'async',
    ));
    return $image;
}

/**
 * Performance Optimization Functions
 */

/**
 * Minify HTML output
 */
function gp_discover_pro_minify_html($buffer) {
    if (!is_admin() && !is_feed() && !is_robots()) {
        $buffer = preg_replace('/\s+/', ' ', $buffer);
        $buffer = preg_replace('/<!--(?!<!)[^\[>].*?-->/', '', $buffer);
    }
    return $buffer;
}

// Enable HTML minification
if (!is_admin()) {
    ob_start('gp_discover_pro_minify_html');
}

/**
 * Remove query strings from static resources
 */
function gp_discover_pro_remove_query_strings($src) {
    $parts = explode('?ver', $src);
    return $parts[0];
}
add_filter('script_loader_src', 'gp_discover_pro_remove_query_strings', 15, 1);
add_filter('style_loader_src', 'gp_discover_pro_remove_query_strings', 15, 1);

/**
 * Defer non-critical JavaScript
 */
function gp_discover_pro_defer_scripts($tag, $handle, $src) {
    $defer_scripts = array(
        'gp-discover-pro-lazy',
        'gp-discover-pro-performance',
    );
    
    if (in_array($handle, $defer_scripts)) {
        return '<script src="' . $src . '" defer></script>' . "\n";
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'gp_discover_pro_defer_scripts', 10, 3);

/**
 * Security Functions
 */

/**
 * Remove WordPress version from head and feeds
 */
function gp_discover_pro_remove_version() {
    return '';
}
add_filter('the_generator', 'gp_discover_pro_remove_version');

/**
 * Disable XML-RPC
 */
add_filter('xmlrpc_enabled', '__return_false');

/**
 * Remove unnecessary meta tags
 */
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');

/**
 * Prevent direct access to PHP files
 */
function gp_discover_pro_prevent_direct_access() {
    if (!defined('ABSPATH')) {
        header('HTTP/1.0 403 Forbidden');
        exit;
    }
}
add_action('init', 'gp_discover_pro_prevent_direct_access');

/**
 * Custom Post Types and Taxonomies
 */

/**
 * Register custom post types if needed
 */
function gp_discover_pro_register_post_types() {
    // Example: Portfolio post type
    register_post_type('portfolio', array(
        'labels' => array(
            'name' => __('Portfolio', 'gp-discover-pro'),
            'singular_name' => __('Portfolio Item', 'gp-discover-pro'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest' => true,
    ));
}
add_action('init', 'gp_discover_pro_register_post_types');

/**
 * Include additional files
 */
require_once GP_DISCOVER_PRO_PATH . '/inc/customizer.php';
require_once GP_DISCOVER_PRO_PATH . '/inc/performance.php';
require_once GP_DISCOVER_PRO_PATH . '/inc/seo.php';
require_once GP_DISCOVER_PRO_PATH . '/inc/accessibility.php';
require_once GP_DISCOVER_PRO_PATH . '/inc/security.php';
