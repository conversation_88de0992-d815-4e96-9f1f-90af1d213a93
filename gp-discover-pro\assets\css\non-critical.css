/**
 * Non-Critical CSS - Loaded asynchronously
 * 
 * @package GP_Discover_Pro
 * @version 1.0.0
 */

/* ==========================================================================
   ADVANCED ANIMATIONS
   ========================================================================== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* ==========================================================================
   ADVANCED COMPONENTS
   ========================================================================== */

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: var(--space-xl);
    right: var(--space-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.back-to-top:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Social Sharing */
.social-share {
    display: flex;
    gap: var(--space-md);
    margin: var(--space-xl) 0;
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.social-share-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.social-share a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    color: var(--text-white);
    text-decoration: none;
    transition: var(--transition-fast);
    font-size: 1.2rem;
}

.social-share .facebook {
    background: #1877f2;
}

.social-share .twitter {
    background: #1da1f2;
}

.social-share .linkedin {
    background: #0077b5;
}

.social-share .whatsapp {
    background: #25d366;
}

.social-share a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Breadcrumbs */
.breadcrumbs {
    margin: var(--space-lg) 0;
    padding: var(--space-md) 0;
    border-bottom: 1px solid var(--border-light);
}

.breadcrumb-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: var(--space-sm);
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.breadcrumb-item.active span {
    color: var(--text-muted);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin: var(--space-2xl) 0;
    list-style: none;
    padding: 0;
}

.pagination a,
.pagination span {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.pagination a:hover,
.pagination a:focus {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.pagination .current {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* Search Form */
.search-form {
    position: relative;
    max-width: 400px;
    margin: var(--space-lg) 0;
}

.search-form input[type="search"] {
    width: 100%;
    padding: var(--space-md) var(--space-xl) var(--space-md) var(--space-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: var(--bg-primary);
    transition: var(--transition-fast);
}

.search-form input[type="search"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    outline: none;
}

.search-form button {
    position: absolute;
    right: var(--space-sm);
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-form button:hover {
    background: var(--primary-hover);
}

/* Comments */
.comments-area {
    margin: var(--space-2xl) 0;
    padding: var(--space-xl);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.comments-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--space-xl);
    color: var(--text-primary);
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.comment-author {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.comment-author img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-author-name {
    font-weight: 600;
    color: var(--text-primary);
}

.comment-date {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.comment-content {
    line-height: 1.6;
    color: var(--text-secondary);
}

.comment-reply-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    margin-top: var(--space-md);
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.comment-reply-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* Comment Form */
.comment-form {
    margin-top: var(--space-xl);
    padding: var(--space-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.comment-form-comment textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--space-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    transition: var(--transition-fast);
}

.comment-form-comment textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    outline: none;
}

.form-submit {
    margin-top: var(--space-lg);
}

.submit {
    background: var(--primary-color);
    color: var(--text-white);
    border: none;
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.submit:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

/* ==========================================================================
   ADVANCED RESPONSIVE UTILITIES
   ========================================================================== */

/* Responsive Text Alignment */
@media (max-width: 768px) {
    .text-center-mobile { text-align: center; }
    .text-left-mobile { text-align: left; }
    .text-right-mobile { text-align: right; }
}

@media (min-width: 769px) {
    .text-center-desktop { text-align: center; }
    .text-left-desktop { text-align: left; }
    .text-right-desktop { text-align: right; }
}

/* Responsive Display */
@media (max-width: 768px) {
    .hidden-mobile { display: none !important; }
    .block-mobile { display: block !important; }
    .flex-mobile { display: flex !important; }
    .grid-mobile { display: grid !important; }
}

@media (min-width: 769px) {
    .hidden-desktop { display: none !important; }
    .block-desktop { display: block !important; }
    .flex-desktop { display: flex !important; }
    .grid-desktop { display: grid !important; }
}

/* Responsive Spacing */
@media (max-width: 768px) {
    .p-mobile-sm { padding: var(--space-sm); }
    .p-mobile-md { padding: var(--space-md); }
    .p-mobile-lg { padding: var(--space-lg); }
    
    .m-mobile-sm { margin: var(--space-sm); }
    .m-mobile-md { margin: var(--space-md); }
    .m-mobile-lg { margin: var(--space-lg); }
}

/* ==========================================================================
   PRINT STYLES
   ========================================================================== */

@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        font-weight: bold;
    }
    
    p, blockquote {
        orphans: 3;
        widows: 3;
    }
    
    blockquote {
        border-left: 4px solid #ccc;
        padding-left: 1em;
        margin: 1em 0;
    }
    
    a {
        text-decoration: underline;
    }
    
    a[href^="http"]:after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
    }
    
    img {
        max-width: 100% !important;
        page-break-inside: avoid;
    }
    
    .no-print,
    .back-to-top,
    .social-share,
    .comments-area,
    .comment-form,
    .main-navigation,
    .search-form {
        display: none !important;
    }
    
    .site-header {
        border-bottom: 1px solid #ccc;
        padding-bottom: 1em;
        margin-bottom: 2em;
    }
    
    .site-title {
        font-size: 24pt;
        margin-bottom: 0.5em;
    }
    
    .entry-title {
        font-size: 18pt;
        margin-bottom: 1em;
    }
    
    .entry-content {
        font-size: 11pt;
        line-height: 1.5;
    }
}
