/* EMERGENCY FIX - Copy this to WordPress Customizer → Additional CSS */

/* ULTRA AGGRESSIVE CATEGORY HEADER OVERRIDE */
.gb-text-4666a877,
.gb-text-54ea9bb4,
h2.gb-text-4666a877,
h2.gb-text-54ea9bb4,
.gb-text.gb-text-4666a877,
.gb-text.gb-text-54ea9bb4,
[class*="gb-text-4666a877"],
[class*="gb-text-54ea9bb4"] {
    /* REMOVE ALL FANCY STYLING */
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    background-gradient: none !important;
    
    /* REMOVE BORDERS AND SHADOWS */
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    text-shadow: none !important;
    
    /* REMOVE PADDING */
    padding: 0 !important;
    margin: 0 0 20px 0 !important;
    
    /* SIMPLE TEXT STYLING */
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 24px !important;
    text-align: left !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    line-height: 1.3 !important;
    
    /* REMOVE ANIMATIONS */
    transition: none !important;
    animation: none !important;
    transform: none !important;
    
    /* SIMPLE DISPLAY */
    display: block !important;
    position: static !important;
    overflow: visible !important;
    
    /* OVERRIDE CSS VARIABLES */
    --gb-background-color: transparent !important;
    --gb-text-color: #333333 !important;
    --gb-padding: 0 !important;
    --gb-margin-bottom: 20px !important;
    --gb-border-radius: 0 !important;
    --gb-box-shadow: none !important;
}

/* REMOVE ALL PSEUDO-ELEMENTS */
.gb-text-4666a877:before,
.gb-text-54ea9bb4:before,
.gb-text-4666a877:after,
.gb-text-54ea9bb4:after,
h2.gb-text-4666a877:before,
h2.gb-text-54ea9bb4:before,
h2.gb-text-4666a877:after,
h2.gb-text-54ea9bb4:after,
[class*="gb-text-4666a877"]:before,
[class*="gb-text-54ea9bb4"]:before,
[class*="gb-text-4666a877"]:after,
[class*="gb-text-54ea9bb4"]:after {
    display: none !important;
    content: none !important;
    background: none !important;
}

/* SIMPLE HOVER EFFECT */
.gb-text-4666a877:hover,
.gb-text-54ea9bb4:hover,
h2.gb-text-4666a877:hover,
h2.gb-text-54ea9bb4:hover {
    color: #ff6b35 !important;
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

/* OVERRIDE CONTAINER STYLES */
.gb-element .gb-text-4666a877,
.gb-element .gb-text-54ea9bb4,
.gb-container .gb-text-4666a877,
.gb-container .gb-text-54ea9bb4,
.gb-inside-container .gb-text-4666a877,
.gb-inside-container .gb-text-54ea9bb4 {
    background: transparent !important;
    background-color: transparent !important;
    color: #333333 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* MOBILE RESPONSIVE */
@media (max-width: 768px) {
    .gb-text-4666a877,
    .gb-text-54ea9bb4,
    h2.gb-text-4666a877,
    h2.gb-text-54ea9bb4 {
        font-size: 20px !important;
        margin-bottom: 15px !important;
    }
}

/* TABLET RESPONSIVE */
@media (max-width: 1024px) {
    .gb-text-4666a877,
    .gb-text-54ea9bb4,
    h2.gb-text-4666a877,
    h2.gb-text-54ea9bb4 {
        font-size: 22px !important;
    }
}

/* FORCE OVERRIDE ANY INLINE STYLES */
.gb-text-4666a877[style],
.gb-text-54ea9bb4[style],
h2.gb-text-4666a877[style],
h2.gb-text-54ea9bb4[style] {
    background: transparent !important;
    background-color: transparent !important;
    color: #333333 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    text-shadow: none !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    text-align: left !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
    margin: 0 0 20px 0 !important;
}

/* CLEAN SOCIAL MEDIA STYLING */
.social-theme-consistent {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 25px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #ff6b35;
}

.social-theme-text {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
    margin-right: 10px;
}

.social-theme-icons {
    display: flex;
    gap: 10px;
}

.social-theme-icon {
    width: 44px;
    height: 44px;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid #fef2e7;
    background: #ffffff;
    color: #34495e;
}

.social-theme-icon:hover {
    background: #ff6b35;
    color: #ffffff;
    border-color: #ff6b35;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

/* CLEAN POST STYLING */
.gb-text-d67b07a7 a,
.gb-text-b56eacd4 a,
.gb-text-6b888b19 a {
    color: #2c3e50 !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.gb-text-d67b07a7 a:hover,
.gb-text-b56eacd4 a:hover,
.gb-text-6b888b19 a:hover {
    color: #ff6b35 !important;
}

/* VERIFICATION BADGE */
.gb-shape svg {
    color: #ff6b35 !important;
}

/* AUTHOR AVATAR */
.gb-media-7e79f4af,
.gb-media-e333169e,
.gb-media-bf31265d {
    border: 1.5px solid #ff6b35 !important;
    border-radius: 50% !important;
}

/* PAGINATION */
.gb-query-loop-pagination a {
    background: #ffffff !important;
    color: #2c3e50 !important;
    border: 1px solid #e9ecef !important;
    border-radius: 7px !important;
    padding: 12px 16px !important;
    margin: 0 4px !important;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.gb-query-loop-pagination a:hover {
    background: #ff6b35 !important;
    color: #ffffff !important;
    border-color: #ff6b35 !important;
}

/* POST CARDS */
.gb-loop-item {
    background: #ffffff;
    border-radius: 7px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.gb-loop-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* MOBILE TOUCH TARGETS */
@media (max-width: 768px) {
    .gb-text a {
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        padding: 8px 4px;
    }
    
    .gb-media a {
        display: block;
        min-height: 44px;
    }
    
    .social-theme-consistent {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .social-theme-icons {
        width: 100%;
        justify-content: center;
    }
}
