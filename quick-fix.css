/* Quick Fix CSS - Copy this to WordPress Customizer → Additional CSS */

/* Clean Headers */
.gb-text-4666a877,
.gb-text-54ea9bb4 {
    background: none !important;
    color: #333 !important;
    padding: 0 !important;
    border: none !important;
    font-weight: 600 !important;
    font-size: 24px !important;
    text-align: left !important;
    margin-bottom: 20px !important;
    text-transform: capitalize !important;
}

/* Theme Colors */
:root {
    --background: #ffffff;
    --text-1: #2c3e50;
    --text-2: #34495e;
    --link-text: #e67e22;
    --color-background: #ff6b35;
    --color-background-hover: #e55a2b;
    --border-color: #fef2e7;
}

/* Accessibility - Touch Targets */
@media (max-width: 768px) {
    .main-navigation a {
        min-height: 44px;
        padding: 12px 15px;
    }
    
    .wpjankari-social-sharing {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        margin: 6px;
    }
    
    .gb-query-loop-pagination a {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }
}

/* Social Icons - Theme Consistent */
.social-theme-consistent {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
}

.social-theme-text {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.social-theme-icon {
    width: 40px;
    height: 40px;
    border-radius: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    color: #34495e;
    border: 1px solid #fef2e7;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-theme-icon:hover {
    background: #ff6b35;
    color: #ffffff;
    border-color: #ff6b35;
    transform: translateY(-2px);
}

/* Hindi Content Optimization */
body {
    line-height: 1.7;
    word-spacing: 0.1em;
}

.entry-content p {
    margin-bottom: 1.2em;
    text-align: justify;
}

/* Focus Indicators */
a:focus,
button:focus,
input:focus {
    outline: 2px solid #ff6b35;
    outline-offset: 2px;
}

/* Better Contrast */
.gb-text a {
    color: #e67e22;
}

.gb-text a:hover {
    color: #ff6b35;
}
