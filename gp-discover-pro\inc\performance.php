<?php
/**
 * Performance Optimization
 * 
 * @package GP_Discover_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Performance Optimization Class
 */
class GP_Discover_Pro_Performance {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init_performance_optimizations'));
        add_action('wp_enqueue_scripts', array($this, 'optimize_scripts_styles'));
        add_filter('wp_get_attachment_image_attributes', array($this, 'add_lazy_loading_attributes'));
        add_filter('the_content', array($this, 'add_lazy_loading_to_images'));
        add_action('wp_head', array($this, 'add_resource_hints'), 1);
        add_action('wp_footer', array($this, 'add_service_worker'), 99);
        add_filter('script_loader_tag', array($this, 'add_async_defer_attributes'), 10, 3);
        add_filter('style_loader_tag', array($this, 'add_preload_styles'), 10, 4);
    }
    
    /**
     * Initialize Performance Optimizations
     */
    public function init_performance_optimizations() {
        // Remove unnecessary WordPress features
        $this->remove_unnecessary_features();
        
        // Optimize database queries
        $this->optimize_database_queries();
        
        // Enable GZIP compression
        $this->enable_gzip_compression();
        
        // Set cache headers
        $this->set_cache_headers();
        
        // Optimize images
        $this->optimize_images();
    }
    
    /**
     * Remove Unnecessary WordPress Features
     */
    private function remove_unnecessary_features() {
        // Remove emoji scripts and styles
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
        remove_action('admin_print_scripts', 'print_emoji_detection_script');
        remove_action('admin_print_styles', 'print_emoji_styles');
        remove_filter('the_content_feed', 'wp_staticize_emoji');
        remove_filter('comment_text_rss', 'wp_staticize_emoji');
        remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
        
        // Remove unnecessary meta tags
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');
        
        // Remove REST API links
        remove_action('wp_head', 'rest_output_link_wp_head');
        remove_action('wp_head', 'wp_oembed_add_discovery_links');
        
        // Disable embeds
        add_action('init', function() {
            remove_action('rest_api_init', 'wp_oembed_register_route');
            add_filter('embed_oembed_discover', '__return_false');
            remove_filter('oembed_dataparse', 'wp_filter_oembed_result', 10);
            remove_action('wp_head', 'wp_oembed_add_discovery_links');
            remove_action('wp_head', 'wp_oembed_add_host_js');
        });
        
        // Remove query strings from static resources
        add_filter('script_loader_src', array($this, 'remove_query_strings'), 15, 1);
        add_filter('style_loader_src', array($this, 'remove_query_strings'), 15, 1);
    }
    
    /**
     * Optimize Scripts and Styles
     */
    public function optimize_scripts_styles() {
        // Dequeue unnecessary scripts on frontend
        if (!is_admin()) {
            wp_dequeue_script('wp-embed');
            wp_dequeue_script('comment-reply');
            
            // Conditionally load scripts
            if (!is_singular() || !comments_open()) {
                wp_dequeue_script('comment-reply');
            }
            
            // Remove block library CSS on non-block pages
            if (!has_blocks()) {
                wp_dequeue_style('wp-block-library');
                wp_dequeue_style('wp-block-library-theme');
                wp_dequeue_style('wc-blocks-style');
            }
        }
        
        // Minify CSS if enabled
        if (get_theme_mod('minify_css', true)) {
            add_filter('style_loader_tag', array($this, 'minify_css_output'), 10, 4);
        }
    }
    
    /**
     * Add Lazy Loading Attributes
     */
    public function add_lazy_loading_attributes($attr) {
        if (get_theme_mod('enable_lazy_loading', true)) {
            $attr['loading'] = 'lazy';
            $attr['decoding'] = 'async';
        }
        return $attr;
    }
    
    /**
     * Add Lazy Loading to Content Images
     */
    public function add_lazy_loading_to_images($content) {
        if (get_theme_mod('enable_lazy_loading', true) && !is_admin() && !is_feed()) {
            // Add loading="lazy" to images
            $content = preg_replace('/<img((?:[^>](?!loading=))*+)>/i', '<img$1 loading="lazy" decoding="async">', $content);
            
            // Add lazy loading class for JavaScript fallback
            $content = preg_replace('/<img([^>]*?)class="([^"]*?)"([^>]*?)>/i', '<img$1class="$2 lazy-load"$3>', $content);
            $content = preg_replace('/<img((?:[^>](?!class=))*+)>/i', '<img$1 class="lazy-load">', $content);
        }
        return $content;
    }
    
    /**
     * Add Resource Hints
     */
    public function add_resource_hints() {
        // DNS prefetch for external resources
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
        echo '<link rel="dns-prefetch" href="//fonts.gstatic.com">' . "\n";
        echo '<link rel="dns-prefetch" href="//www.google-analytics.com">' . "\n";
        
        // Preconnect to critical third-party origins
        echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
        echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
        
        // Preload critical resources
        $this->preload_critical_resources();
    }
    
    /**
     * Preload Critical Resources
     */
    private function preload_critical_resources() {
        // Preload critical CSS
        echo '<link rel="preload" href="' . get_stylesheet_uri() . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
        
        // Preload critical fonts
        $critical_fonts = array(
            'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
        );
        
        foreach ($critical_fonts as $font_url) {
            echo '<link rel="preload" href="' . esc_url($font_url) . '" as="font" type="font/woff2" crossorigin>' . "\n";
        }
        
        // Preload hero image on homepage
        if (is_front_page()) {
            $hero_image = get_theme_mod('hero_background_image');
            if ($hero_image) {
                echo '<link rel="preload" href="' . esc_url($hero_image) . '" as="image">' . "\n";
            }
        }
    }
    
    /**
     * Add Service Worker
     */
    public function add_service_worker() {
        if (get_theme_mod('enable_service_worker', true)) {
            ?>
            <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('<?php echo get_stylesheet_directory_uri(); ?>/assets/js/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    }).catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
                });
            }
            </script>
            <?php
        }
    }
    
    /**
     * Add Async/Defer Attributes to Scripts
     */
    public function add_async_defer_attributes($tag, $handle, $src) {
        // Scripts to defer
        $defer_scripts = array(
            'gp-discover-pro-lazy',
            'gp-discover-pro-performance',
            'google-analytics',
        );
        
        // Scripts to load async
        $async_scripts = array(
            'gp-discover-pro-social',
        );
        
        if (in_array($handle, $defer_scripts)) {
            return str_replace('<script ', '<script defer ', $tag);
        }
        
        if (in_array($handle, $async_scripts)) {
            return str_replace('<script ', '<script async ', $tag);
        }
        
        return $tag;
    }
    
    /**
     * Add Preload for Non-Critical Styles
     */
    public function add_preload_styles($html, $handle, $href, $media) {
        $non_critical_styles = array(
            'gp-discover-pro-non-critical',
            'dashicons',
        );
        
        if (in_array($handle, $non_critical_styles)) {
            $html = '<link rel="preload" href="' . $href . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
            $html .= '<noscript><link rel="stylesheet" href="' . $href . '"></noscript>';
        }
        
        return $html;
    }
    
    /**
     * Remove Query Strings
     */
    public function remove_query_strings($src) {
        $parts = explode('?ver', $src);
        return $parts[0];
    }
    
    /**
     * Minify CSS Output
     */
    public function minify_css_output($html, $handle, $href, $media) {
        // Only minify our theme styles
        if (strpos($handle, 'gp-discover-pro') !== false) {
            // This would typically integrate with a CSS minification service
            // For now, we'll add a filter hook for future implementation
            $html = apply_filters('gp_discover_pro_minify_css', $html, $handle, $href, $media);
        }
        return $html;
    }
    
    /**
     * Optimize Database Queries
     */
    private function optimize_database_queries() {
        // Remove unnecessary queries
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);
        
        // Optimize post queries
        add_action('pre_get_posts', array($this, 'optimize_post_queries'));
    }
    
    /**
     * Optimize Post Queries
     */
    public function optimize_post_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limit post revisions
            if (!defined('WP_POST_REVISIONS')) {
                define('WP_POST_REVISIONS', 3);
            }
            
            // Optimize search queries
            if ($query->is_search()) {
                $query->set('posts_per_page', 10);
                $query->set('post_type', array('post', 'page'));
            }
        }
    }
    
    /**
     * Enable GZIP Compression
     */
    private function enable_gzip_compression() {
        if (!ob_get_level()) {
            ob_start('ob_gzhandler');
        }
    }
    
    /**
     * Set Cache Headers
     */
    private function set_cache_headers() {
        if (!is_admin()) {
            // Set cache headers for static assets
            add_action('wp_head', function() {
                if (!headers_sent()) {
                    header('Cache-Control: public, max-age=31536000');
                    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
                }
            });
        }
    }
    
    /**
     * Optimize Images
     */
    private function optimize_images() {
        // Add WebP support
        if (get_theme_mod('enable_webp', true)) {
            add_filter('wp_generate_attachment_metadata', array($this, 'generate_webp_images'));
        }
        
        // Optimize image sizes
        add_filter('intermediate_image_sizes_advanced', array($this, 'remove_unused_image_sizes'));
        
        // Add responsive images
        add_filter('wp_get_attachment_image_attributes', array($this, 'add_responsive_image_attributes'));
    }
    
    /**
     * Generate WebP Images
     */
    public function generate_webp_images($metadata) {
        if (!function_exists('imagewebp')) {
            return $metadata;
        }
        
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/' . $metadata['file'];
        
        if (file_exists($file_path)) {
            $webp_path = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $file_path);
            
            $image_type = wp_check_filetype($file_path)['type'];
            
            switch ($image_type) {
                case 'image/jpeg':
                    $image = imagecreatefromjpeg($file_path);
                    break;
                case 'image/png':
                    $image = imagecreatefrompng($file_path);
                    break;
                default:
                    return $metadata;
            }
            
            if ($image) {
                imagewebp($image, $webp_path, 80);
                imagedestroy($image);
            }
        }
        
        return $metadata;
    }
    
    /**
     * Remove Unused Image Sizes
     */
    public function remove_unused_image_sizes($sizes) {
        // Remove unused WordPress default sizes
        unset($sizes['medium_large']);
        unset($sizes['1536x1536']);
        unset($sizes['2048x2048']);
        
        return $sizes;
    }
    
    /**
     * Add Responsive Image Attributes
     */
    public function add_responsive_image_attributes($attr) {
        $attr['sizes'] = '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw';
        return $attr;
    }
}

// Initialize Performance Optimization
new GP_Discover_Pro_Performance();
