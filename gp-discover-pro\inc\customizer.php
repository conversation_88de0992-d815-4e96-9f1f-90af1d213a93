<?php
/**
 * Customizer Settings
 * 
 * @package GP_Discover_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Customizer Class
 */
class GP_Discover_Pro_Customizer {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('customize_register', array($this, 'register_customizer_settings'));
        add_action('wp_head', array($this, 'output_customizer_css'));
    }
    
    /**
     * Register Customizer Settings
     */
    public function register_customizer_settings($wp_customize) {
        
        // Theme Options Panel
        $wp_customize->add_panel('gp_discover_pro_options', array(
            'title' => __('GP Discover Pro Options', 'gp-discover-pro'),
            'description' => __('Customize your theme settings', 'gp-discover-pro'),
            'priority' => 30,
        ));
        
        // Colors Section
        $wp_customize->add_section('gp_discover_pro_colors', array(
            'title' => __('Theme Colors', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 10,
        ));
        
        // Primary Color
        $wp_customize->add_setting('primary_color', array(
            'default' => '#ff6b35',
            'sanitize_callback' => 'sanitize_hex_color',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_color', array(
            'label' => __('Primary Color', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_colors',
            'settings' => 'primary_color',
        )));
        
        // Secondary Color
        $wp_customize->add_setting('secondary_color', array(
            'default' => '#2c3e50',
            'sanitize_callback' => 'sanitize_hex_color',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'secondary_color', array(
            'label' => __('Secondary Color', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_colors',
            'settings' => 'secondary_color',
        )));
        
        // Typography Section
        $wp_customize->add_section('gp_discover_pro_typography', array(
            'title' => __('Typography', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 20,
        ));
        
        // Font Family
        $wp_customize->add_setting('font_family', array(
            'default' => 'Inter',
            'sanitize_callback' => 'sanitize_text_field',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control('font_family', array(
            'label' => __('Font Family', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_typography',
            'type' => 'select',
            'choices' => array(
                'Inter' => 'Inter',
                'Roboto' => 'Roboto',
                'Open Sans' => 'Open Sans',
                'Lato' => 'Lato',
                'Poppins' => 'Poppins',
            ),
        ));
        
        // Font Size
        $wp_customize->add_setting('base_font_size', array(
            'default' => '16',
            'sanitize_callback' => 'absint',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control('base_font_size', array(
            'label' => __('Base Font Size (px)', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_typography',
            'type' => 'range',
            'input_attrs' => array(
                'min' => 12,
                'max' => 24,
                'step' => 1,
            ),
        ));
        
        // Layout Section
        $wp_customize->add_section('gp_discover_pro_layout', array(
            'title' => __('Layout Options', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 30,
        ));
        
        // Container Width
        $wp_customize->add_setting('container_width', array(
            'default' => '1280',
            'sanitize_callback' => 'absint',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control('container_width', array(
            'label' => __('Container Width (px)', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_layout',
            'type' => 'range',
            'input_attrs' => array(
                'min' => 960,
                'max' => 1920,
                'step' => 20,
            ),
        ));
        
        // Grid Columns
        $wp_customize->add_setting('grid_columns', array(
            'default' => '3',
            'sanitize_callback' => 'absint',
            'transport' => 'postMessage',
        ));
        
        $wp_customize->add_control('grid_columns', array(
            'label' => __('Grid Columns (Desktop)', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_layout',
            'type' => 'select',
            'choices' => array(
                '2' => '2 Columns',
                '3' => '3 Columns',
                '4' => '4 Columns',
            ),
        ));
        
        // Performance Section
        $wp_customize->add_section('gp_discover_pro_performance', array(
            'title' => __('Performance', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 40,
        ));
        
        // Enable Lazy Loading
        $wp_customize->add_setting('enable_lazy_loading', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enable_lazy_loading', array(
            'label' => __('Enable Lazy Loading', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_performance',
            'type' => 'checkbox',
        ));
        
        // Minify CSS
        $wp_customize->add_setting('minify_css', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('minify_css', array(
            'label' => __('Minify CSS', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_performance',
            'type' => 'checkbox',
        ));
        
        // Enable WebP
        $wp_customize->add_setting('enable_webp', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enable_webp', array(
            'label' => __('Enable WebP Images', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_performance',
            'type' => 'checkbox',
        ));
        
        // SEO Section
        $wp_customize->add_section('gp_discover_pro_seo', array(
            'title' => __('SEO Options', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 50,
        ));
        
        // Enable Schema Markup
        $wp_customize->add_setting('enable_schema', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enable_schema', array(
            'label' => __('Enable Schema Markup', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_seo',
            'type' => 'checkbox',
        ));
        
        // Enable Open Graph
        $wp_customize->add_setting('enable_opengraph', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enable_opengraph', array(
            'label' => __('Enable Open Graph Tags', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_seo',
            'type' => 'checkbox',
        ));
        
        // Social Media Section
        $wp_customize->add_section('gp_discover_pro_social', array(
            'title' => __('Social Media', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 60,
        ));
        
        // Social Media Links
        $social_networks = array(
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'instagram' => 'Instagram',
            'linkedin' => 'LinkedIn',
            'youtube' => 'YouTube',
        );
        
        foreach ($social_networks as $network => $label) {
            $wp_customize->add_setting("social_{$network}", array(
                'default' => '',
                'sanitize_callback' => 'esc_url_raw',
            ));
            
            $wp_customize->add_control("social_{$network}", array(
                'label' => sprintf(__('%s URL', 'gp-discover-pro'), $label),
                'section' => 'gp_discover_pro_social',
                'type' => 'url',
            ));
        }
        
        // Accessibility Section
        $wp_customize->add_section('gp_discover_pro_accessibility', array(
            'title' => __('Accessibility', 'gp-discover-pro'),
            'panel' => 'gp_discover_pro_options',
            'priority' => 70,
        ));
        
        // High Contrast Mode
        $wp_customize->add_setting('high_contrast_mode', array(
            'default' => false,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('high_contrast_mode', array(
            'label' => __('Enable High Contrast Mode', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_accessibility',
            'type' => 'checkbox',
        ));
        
        // Skip Links
        $wp_customize->add_setting('enable_skip_links', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enable_skip_links', array(
            'label' => __('Enable Skip Links', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_accessibility',
            'type' => 'checkbox',
        ));
        
        // Focus Indicators
        $wp_customize->add_setting('enhanced_focus_indicators', array(
            'default' => true,
            'sanitize_callback' => 'wp_validate_boolean',
        ));
        
        $wp_customize->add_control('enhanced_focus_indicators', array(
            'label' => __('Enhanced Focus Indicators', 'gp-discover-pro'),
            'section' => 'gp_discover_pro_accessibility',
            'type' => 'checkbox',
        ));
    }
    
    /**
     * Output Customizer CSS
     */
    public function output_customizer_css() {
        $primary_color = get_theme_mod('primary_color', '#ff6b35');
        $secondary_color = get_theme_mod('secondary_color', '#2c3e50');
        $font_family = get_theme_mod('font_family', 'Inter');
        $base_font_size = get_theme_mod('base_font_size', 16);
        $container_width = get_theme_mod('container_width', 1280);
        $grid_columns = get_theme_mod('grid_columns', 3);
        
        echo '<style id="gp-discover-pro-customizer-css">';
        echo ':root {';
        echo '--primary-color: ' . esc_attr($primary_color) . ';';
        echo '--text-primary: ' . esc_attr($secondary_color) . ';';
        echo '--font-primary: "' . esc_attr($font_family) . '", sans-serif;';
        echo '--container-xl: ' . esc_attr($container_width) . 'px;';
        echo '}';
        
        echo 'html { font-size: ' . esc_attr($base_font_size) . 'px; }';
        
        echo '.grid-cols-auto { grid-template-columns: repeat(' . esc_attr($grid_columns) . ', 1fr); }';
        
        // High contrast mode
        if (get_theme_mod('high_contrast_mode', false)) {
            echo 'body { filter: contrast(150%); }';
        }
        
        echo '</style>';
    }
}

// Initialize Customizer
new GP_Discover_Pro_Customizer();
