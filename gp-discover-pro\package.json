{"name": "gp-discover-pro", "version": "1.0.0", "description": "Professional GeneratePress child theme with performance optimization, SEO features, and modern design", "main": "functions.php", "scripts": {"build": "npm run build:css && npm run build:js", "build:css": "npm run minify:css && npm run autoprefixer", "build:js": "npm run minify:js", "minify:css": "cleancss -o assets/css/style.min.css style.css && cleancss -o assets/css/non-critical.min.css assets/css/non-critical.css", "minify:js": "uglifyjs assets/js/main.js -o assets/js/main.min.js -c -m && uglifyjs assets/js/sw.js -o assets/js/sw.min.js -c -m", "autoprefixer": "postcss style.css --use autoprefixer -o style.css && postcss assets/css/non-critical.css --use autoprefixer -o assets/css/non-critical.css", "dev": "npm run watch:css & npm run watch:js", "watch:css": "chokidar 'style.css' 'assets/css/*.css' -c 'npm run build:css'", "watch:js": "chokidar 'assets/js/*.js' '!assets/js/*.min.js' -c 'npm run build:js'", "lint:css": "stylelint 'style.css' 'assets/css/*.css'", "lint:js": "eslint 'assets/js/*.js' '!assets/js/*.min.js'", "lint": "npm run lint:css && npm run lint:js", "test": "npm run lint", "optimize:images": "imagemin 'assets/images/*.{jpg,jpeg,png,gif,svg}' --out-dir=assets/images/optimized", "generate:webp": "cwebp -q 80 assets/images/*.{jpg,jpeg,png} -o assets/images/", "validate:html": "html-validate templates/*.html", "validate:accessibility": "pa11y-ci --sitemap http://localhost/sitemap.xml", "performance:audit": "lighthouse http://localhost --output=json --output-path=./lighthouse-report.json", "security:scan": "npm audit && snyk test", "deploy:staging": "rsync -avz --exclude node_modules . user@staging-server:/path/to/theme/", "deploy:production": "rsync -avz --exclude node_modules --exclude .git . user@production-server:/path/to/theme/"}, "keywords": ["wordpress", "theme", "generatepress", "child-theme", "performance", "seo", "accessibility", "responsive", "modern", "css-grid", "flexbox"], "author": "Professional WordPress Developer", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "https://github.com/your-username/gp-discover-pro.git"}, "bugs": {"url": "https://github.com/your-username/gp-discover-pro/issues"}, "homepage": "https://github.com/your-username/gp-discover-pro#readme", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.0", "chokidar-cli": "^3.0.0", "clean-css-cli": "^5.6.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-wordpress": "^2.0.0", "html-validate": "^8.0.0", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "imagemin-webp": "^7.0.0", "lighthouse": "^10.2.0", "pa11y-ci": "^3.0.1", "postcss": "^8.4.24", "postcss-cli": "^10.1.0", "snyk": "^1.1180.0", "stylelint": "^15.7.0", "stylelint-config-wordpress": "^17.0.0", "uglify-js": "^3.17.4", "webpack": "^5.88.0", "webpack-cli": "^5.1.4"}, "dependencies": {"intersection-observer": "^0.12.2", "lazysizes": "^5.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "config": {"theme_name": "GP Discover Pro", "theme_uri": "https://your-website.com/themes/gp-discover-pro", "author_uri": "https://your-website.com", "text_domain": "gp-discover-pro"}, "wordpress": {"requires": "5.0", "tested": "6.4", "requires_php": "7.4", "template": "generatepress"}, "performance": {"budget": {"css": "50kb", "js": "100kb", "images": "500kb", "total": "1mb"}, "lighthouse": {"performance": 95, "accessibility": 100, "best_practices": 100, "seo": 100}}, "security": {"content_security_policy": {"default_src": "'self'", "script_src": "'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com", "style_src": "'self' 'unsafe-inline' *.googleapis.com", "font_src": "'self' *.gstatic.com data:", "img_src": "'self' data: *.gravatar.com", "frame_ancestors": "'none'"}}, "accessibility": {"wcag_level": "AA", "color_contrast": "4.5:1", "touch_targets": "44px"}}