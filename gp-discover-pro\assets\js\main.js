/**
 * GP Discover Pro Main JavaScript
 * 
 * @package GP_Discover_Pro
 * @version 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Theme Object
     */
    const GPDiscoverPro = {
        
        /**
         * Initialize
         */
        init: function() {
            this.mobileNavigation();
            this.smoothScrolling();
            this.lazyLoading();
            this.accessibilityEnhancements();
            this.performanceOptimizations();
            this.searchEnhancements();
            this.socialSharing();
            this.backToTop();
            this.imageOptimization();
            this.formEnhancements();
        },
        
        /**
         * Mobile Navigation
         */
        mobileNavigation: function() {
            // Create mobile menu toggle if it doesn't exist
            if (!$('.menu-toggle').length) {
                const toggleButton = $('<button class="menu-toggle" aria-label="Toggle navigation menu" aria-expanded="false">' +
                    '<span></span><span></span><span></span>' +
                    '</button>');
                $('.main-navigation').prepend(toggleButton);
            }
            
            // Toggle mobile menu
            $(document).on('click', '.menu-toggle', function(e) {
                e.preventDefault();
                
                const $this = $(this);
                const $menu = $('.main-navigation ul');
                const isExpanded = $this.attr('aria-expanded') === 'true';
                
                $this.toggleClass('active');
                $this.attr('aria-expanded', !isExpanded);
                $menu.toggleClass('show');
                
                // Announce to screen readers
                if (window.announceToScreenReader) {
                    window.announceToScreenReader(
                        isExpanded ? 'Menu collapsed' : 'Menu expanded'
                    );
                }
            });
            
            // Close menu when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.main-navigation').length) {
                    $('.menu-toggle').removeClass('active').attr('aria-expanded', 'false');
                    $('.main-navigation ul').removeClass('show');
                }
            });
            
            // Handle keyboard navigation in menu
            $('.main-navigation a').on('keydown', function(e) {
                const $this = $(this);
                const $menu = $this.closest('ul');
                const $items = $menu.find('a');
                const currentIndex = $items.index($this);
                
                switch(e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % $items.length;
                        $items.eq(nextIndex).focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        const prevIndex = currentIndex === 0 ? $items.length - 1 : currentIndex - 1;
                        $items.eq(prevIndex).focus();
                        break;
                    case 'Escape':
                        $('.menu-toggle').click();
                        $('.menu-toggle').focus();
                        break;
                }
            });
        },
        
        /**
         * Smooth Scrolling
         */
        smoothScrolling: function() {
            // Smooth scroll for anchor links
            $('a[href*="#"]:not([href="#"])').on('click', function(e) {
                const target = $(this.hash);
                if (target.length) {
                    e.preventDefault();
                    
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500, 'swing', function() {
                        // Focus the target for accessibility
                        target.focus();
                        if (!target.is(':focus')) {
                            target.attr('tabindex', '-1').focus();
                        }
                    });
                }
            });
        },
        
        /**
         * Lazy Loading Enhancement
         */
        lazyLoading: function() {
            // Enhanced lazy loading with Intersection Observer
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            
                            // Add loading class
                            img.classList.add('lazy-loading');
                            
                            // Load the image
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.removeAttribute('data-src');
                            }
                            
                            if (img.dataset.srcset) {
                                img.srcset = img.dataset.srcset;
                                img.removeAttribute('data-srcset');
                            }
                            
                            img.onload = function() {
                                img.classList.remove('lazy-loading');
                                img.classList.add('lazy-loaded');
                            };
                            
                            observer.unobserve(img);
                        }
                    });
                });
                
                // Observe all lazy images
                document.querySelectorAll('img[data-src], img[loading="lazy"]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        },
        
        /**
         * Accessibility Enhancements
         */
        accessibilityEnhancements: function() {
            // Add ARIA labels to links without descriptive text
            $('a').each(function() {
                const $this = $(this);
                const linkText = $this.text().trim().toLowerCase();
                const nonDescriptive = ['click here', 'read more', 'more', 'here', 'link'];
                
                if (nonDescriptive.includes(linkText) && !$this.attr('aria-label')) {
                    const context = $this.closest('article, .card').find('h1, h2, h3, h4, h5, h6').first().text();
                    if (context) {
                        $this.attr('aria-label', `${linkText} - ${context}`);
                    }
                }
            });
            
            // Enhance form accessibility
            $('form').each(function() {
                const $form = $(this);
                
                // Add required indicators
                $form.find('input[required], textarea[required], select[required]').each(function() {
                    const $input = $(this);
                    const $label = $form.find(`label[for="${$input.attr('id')}"]`);
                    
                    if ($label.length && !$label.find('.required').length) {
                        $label.append(' <span class="required" aria-label="required">*</span>');
                    }
                });
                
                // Add ARIA describedby for help text
                $form.find('.help-text').each(function() {
                    const $help = $(this);
                    const helpId = 'help-' + Math.random().toString(36).substr(2, 9);
                    $help.attr('id', helpId);
                    
                    const $input = $help.prev('input, textarea, select');
                    if ($input.length) {
                        $input.attr('aria-describedby', helpId);
                    }
                });
            });
            
            // Announce dynamic content changes
            const announceChange = (message) => {
                if (window.announceToScreenReader) {
                    window.announceToScreenReader(message);
                }
            };
            
            // Monitor for dynamic content changes
            if ('MutationObserver' in window) {
                const contentObserver = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            // Announce when new content is added
                            const addedContent = Array.from(mutation.addedNodes)
                                .filter(node => node.nodeType === Node.ELEMENT_NODE)
                                .map(node => node.textContent.trim())
                                .filter(text => text.length > 0);
                            
                            if (addedContent.length > 0) {
                                announceChange('New content loaded');
                            }
                        }
                    });
                });
                
                contentObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        },
        
        /**
         * Performance Optimizations
         */
        performanceOptimizations: function() {
            // Preload critical resources
            const preloadResource = (href, as, type = null) => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = href;
                link.as = as;
                if (type) link.type = type;
                document.head.appendChild(link);
            };
            
            // Preload next page on hover (for internal links)
            let preloadTimer;
            $('a[href^="/"], a[href^="' + window.location.origin + '"]').on('mouseenter', function() {
                const href = $(this).attr('href');
                preloadTimer = setTimeout(() => {
                    preloadResource(href, 'document');
                }, 200);
            }).on('mouseleave', function() {
                clearTimeout(preloadTimer);
            });
            
            // Optimize images on scroll
            let scrollTimer;
            $(window).on('scroll', function() {
                clearTimeout(scrollTimer);
                scrollTimer = setTimeout(() => {
                    // Lazy load images that are about to come into view
                    const windowHeight = $(window).height();
                    const scrollTop = $(window).scrollTop();
                    
                    $('img[data-src]').each(function() {
                        const $img = $(this);
                        const imgTop = $img.offset().top;
                        
                        if (imgTop < scrollTop + windowHeight + 200) {
                            $img.attr('src', $img.data('src')).removeAttr('data-src');
                        }
                    });
                }, 100);
            });
        },
        
        /**
         * Search Enhancements
         */
        searchEnhancements: function() {
            const $searchForm = $('.search-form, #searchform');
            const $searchInput = $searchForm.find('input[type="search"], input[name="s"]');
            
            if ($searchInput.length) {
                // Add search suggestions (basic implementation)
                $searchInput.on('input', function() {
                    const query = $(this).val();
                    if (query.length > 2) {
                        // Debounce search suggestions
                        clearTimeout(this.searchTimer);
                        this.searchTimer = setTimeout(() => {
                            // Here you would implement AJAX search suggestions
                            console.log('Search query:', query);
                        }, 300);
                    }
                });
                
                // Clear search on escape
                $searchInput.on('keydown', function(e) {
                    if (e.key === 'Escape') {
                        $(this).val('').blur();
                    }
                });
            }
        },
        
        /**
         * Social Sharing
         */
        socialSharing: function() {
            // Add social sharing functionality
            $('.social-share').on('click', 'a', function(e) {
                e.preventDefault();
                
                const url = $(this).attr('href');
                const width = 600;
                const height = 400;
                const left = (screen.width - width) / 2;
                const top = (screen.height - height) / 2;
                
                window.open(url, 'social-share', 
                    `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
                );
            });
        },
        
        /**
         * Back to Top Button
         */
        backToTop: function() {
            // Create back to top button if it doesn't exist
            if (!$('.back-to-top').length) {
                $('body').append(
                    '<button class="back-to-top" aria-label="Back to top" style="display: none;">' +
                    '<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">' +
                    '<path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>' +
                    '</svg></button>'
                );
            }
            
            const $backToTop = $('.back-to-top');
            
            // Show/hide based on scroll position
            $(window).on('scroll', function() {
                if ($(this).scrollTop() > 300) {
                    $backToTop.fadeIn();
                } else {
                    $backToTop.fadeOut();
                }
            });
            
            // Smooth scroll to top
            $backToTop.on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({ scrollTop: 0 }, 500);
            });
        },
        
        /**
         * Image Optimization
         */
        imageOptimization: function() {
            // Add WebP support detection
            const supportsWebP = () => {
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
            };
            
            if (supportsWebP()) {
                document.documentElement.classList.add('webp-support');
                
                // Replace image sources with WebP versions if available
                $('img').each(function() {
                    const $img = $(this);
                    const src = $img.attr('src');
                    
                    if (src && !src.includes('.webp')) {
                        const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
                        
                        // Check if WebP version exists
                        const testImg = new Image();
                        testImg.onload = function() {
                            $img.attr('src', webpSrc);
                        };
                        testImg.src = webpSrc;
                    }
                });
            }
        },
        
        /**
         * Form Enhancements
         */
        formEnhancements: function() {
            // Add form validation enhancements
            $('form').each(function() {
                const $form = $(this);
                
                // Real-time validation
                $form.find('input, textarea, select').on('blur', function() {
                    const $field = $(this);
                    const value = $field.val();
                    const type = $field.attr('type');
                    
                    // Remove previous error states
                    $field.removeClass('error').attr('aria-invalid', 'false');
                    $field.next('.error-message').remove();
                    
                    // Validate required fields
                    if ($field.prop('required') && !value.trim()) {
                        this.showFieldError($field, 'This field is required');
                        return;
                    }
                    
                    // Validate email
                    if (type === 'email' && value && !this.isValidEmail(value)) {
                        this.showFieldError($field, 'Please enter a valid email address');
                        return;
                    }
                    
                    // Validate URL
                    if (type === 'url' && value && !this.isValidURL(value)) {
                        this.showFieldError($field, 'Please enter a valid URL');
                        return;
                    }
                }.bind(this));
                
                // Form submission
                $form.on('submit', function(e) {
                    let hasErrors = false;
                    
                    // Validate all fields
                    $form.find('input[required], textarea[required], select[required]').each(function() {
                        const $field = $(this);
                        if (!$field.val().trim()) {
                            GPDiscoverPro.showFieldError($field, 'This field is required');
                            hasErrors = true;
                        }
                    });
                    
                    if (hasErrors) {
                        e.preventDefault();
                        // Focus first error field
                        $form.find('.error').first().focus();
                    }
                });
            });
        },
        
        /**
         * Show Field Error
         */
        showFieldError: function($field, message) {
            $field.addClass('error').attr('aria-invalid', 'true');
            
            const errorId = 'error-' + Math.random().toString(36).substr(2, 9);
            const $error = $('<div class="error-message" id="' + errorId + '">' + message + '</div>');
            
            $field.attr('aria-describedby', errorId);
            $field.after($error);
        },
        
        /**
         * Email Validation
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        /**
         * URL Validation
         */
        isValidURL: function(url) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        }
    };
    
    /**
     * Initialize on document ready
     */
    $(document).ready(function() {
        GPDiscoverPro.init();
    });
    
    /**
     * Make GPDiscoverPro globally available
     */
    window.GPDiscoverPro = GPDiscoverPro;
    
})(jQuery);
