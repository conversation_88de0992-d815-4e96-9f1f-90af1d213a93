/**
 * Service Worker for GP Discover Pro
 * Provides offline caching and performance optimization
 * 
 * @package GP_Discover_Pro
 * @version 1.0.0
 */

const CACHE_NAME = 'gp-discover-pro-v1.0.0';
const STATIC_CACHE = 'gp-discover-pro-static-v1.0.0';
const DYNAMIC_CACHE = 'gp-discover-pro-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
    '/',
    '/wp-content/themes/gp-discover-pro/style.css',
    '/wp-content/themes/gp-discover-pro/assets/js/main.min.js',
    '/wp-content/themes/gp-discover-pro/assets/css/non-critical.css',
    '/wp-content/themes/gp-discover-pro/assets/images/logo.png',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+Devanagari:wght@300;400;500;600;700;800&display=swap',
];

// Network-first resources (always try network first)
const NETWORK_FIRST = [
    '/wp-admin/',
    '/wp-login.php',
    '/wp-json/',
    '/feed/',
    '/comments/',
];

// Cache-first resources (serve from cache if available)
const CACHE_FIRST = [
    '.css',
    '.js',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.webp',
    '.svg',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot',
];

/**
 * Install Event
 * Cache static assets
 */
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static assets');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('Service Worker: Static assets cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static assets', error);
            })
    );
});

/**
 * Activate Event
 * Clean up old caches
 */
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

/**
 * Fetch Event
 * Handle network requests with caching strategies
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests (except fonts)
    if (url.origin !== location.origin && !url.hostname.includes('googleapis.com') && !url.hostname.includes('gstatic.com')) {
        return;
    }
    
    // Network-first strategy for admin and dynamic content
    if (NETWORK_FIRST.some(path => url.pathname.includes(path))) {
        event.respondWith(networkFirst(request));
        return;
    }
    
    // Cache-first strategy for static assets
    if (CACHE_FIRST.some(ext => url.pathname.includes(ext))) {
        event.respondWith(cacheFirst(request));
        return;
    }
    
    // Stale-while-revalidate for HTML pages
    if (request.headers.get('accept').includes('text/html')) {
        event.respondWith(staleWhileRevalidate(request));
        return;
    }
    
    // Default to network first
    event.respondWith(networkFirst(request));
});

/**
 * Network First Strategy
 * Try network first, fallback to cache
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Service Worker: Network failed, trying cache', error);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page for HTML requests
        if (request.headers.get('accept').includes('text/html')) {
            return caches.match('/offline.html') || new Response('Offline', {
                status: 503,
                statusText: 'Service Unavailable'
            });
        }
        
        throw error;
    }
}

/**
 * Cache First Strategy
 * Try cache first, fallback to network
 */
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Service Worker: Cache and network failed', error);
        throw error;
    }
}

/**
 * Stale While Revalidate Strategy
 * Serve from cache immediately, update cache in background
 */
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Fetch from network in background
    const networkResponsePromise = fetch(request).then(response => {
        if (response.ok) {
            cache.put(request, response.clone());
        }
        return response;
    }).catch(error => {
        console.log('Service Worker: Background fetch failed', error);
    });
    
    // Return cached response immediately if available
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // Otherwise wait for network response
    return networkResponsePromise;
}

/**
 * Background Sync Event
 * Handle offline form submissions
 */
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        console.log('Service Worker: Background sync triggered');
        event.waitUntil(handleBackgroundSync());
    }
});

/**
 * Handle Background Sync
 */
async function handleBackgroundSync() {
    // Handle offline form submissions stored in IndexedDB
    try {
        const db = await openDB();
        const transaction = db.transaction(['offline-forms'], 'readonly');
        const store = transaction.objectStore('offline-forms');
        const forms = await store.getAll();
        
        for (const form of forms) {
            try {
                const response = await fetch(form.url, {
                    method: form.method,
                    headers: form.headers,
                    body: form.data
                });
                
                if (response.ok) {
                    // Remove from offline storage
                    const deleteTransaction = db.transaction(['offline-forms'], 'readwrite');
                    const deleteStore = deleteTransaction.objectStore('offline-forms');
                    await deleteStore.delete(form.id);
                    
                    console.log('Service Worker: Offline form submitted successfully');
                }
            } catch (error) {
                console.error('Service Worker: Failed to submit offline form', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

/**
 * Push Event
 * Handle push notifications
 */
self.addEventListener('push', event => {
    if (!event.data) {
        return;
    }
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/wp-content/themes/gp-discover-pro/assets/images/icon-192x192.png',
        badge: '/wp-content/themes/gp-discover-pro/assets/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: data.primaryKey || 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Read More',
                icon: '/wp-content/themes/gp-discover-pro/assets/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/wp-content/themes/gp-discover-pro/assets/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

/**
 * Notification Click Event
 */
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

/**
 * Message Event
 * Handle messages from main thread
 */
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then(cache => cache.addAll(event.data.urls))
        );
    }
});

/**
 * Open IndexedDB
 */
function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('gp-discover-pro-db', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            if (!db.objectStoreNames.contains('offline-forms')) {
                const store = db.createObjectStore('offline-forms', { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };
    });
}

/**
 * Utility: Clean old caches
 */
async function cleanOldCaches() {
    const cacheNames = await caches.keys();
    const oldCaches = cacheNames.filter(name => 
        name.startsWith('gp-discover-pro-') && 
        name !== STATIC_CACHE && 
        name !== DYNAMIC_CACHE
    );
    
    return Promise.all(oldCaches.map(name => caches.delete(name)));
}

/**
 * Utility: Preload critical resources
 */
async function preloadCriticalResources() {
    const cache = await caches.open(STATIC_CACHE);
    
    const criticalResources = [
        '/',
        '/wp-content/themes/gp-discover-pro/style.css',
        '/wp-content/themes/gp-discover-pro/assets/js/main.min.js'
    ];
    
    return cache.addAll(criticalResources);
}

// Preload critical resources on install
self.addEventListener('install', event => {
    event.waitUntil(
        Promise.all([
            preloadCriticalResources(),
            cleanOldCaches()
        ])
    );
});
