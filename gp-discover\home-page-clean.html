<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern responsive Hindi news theme with clean design and accessibility features">
    <meta name="keywords" content="Hindi news, WordPress theme, responsive design, accessibility">
    <title>GP Discover Pro - Modern Hindi News Theme</title>
    <link rel="stylesheet" href="child-theme/gp-discover-pro/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Noto+Sans+Devanagari:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body class="modern-theme">
    <!-- Skip Link for Accessibility -->
    <a class="skip-link screen-reader-text" href="#main">मुख्य सामग्री पर जाएं</a>

    <!-- Modern Header -->
    <header class="site-header">
        <div class="container">
            <div class="site-branding">
                <h1 class="site-title">GP Discover Pro</h1>
                <p class="site-description">आधुनिक हिंदी समाचार थीम</p>
            </div>

            <nav class="main-navigation" role="navigation" aria-label="मुख्य नेवीगेशन">
                <ul>
                    <li><a href="#home" class="current">होम</a></li>
                    <li><a href="#news">समाचार</a></li>
                    <li><a href="#sports">खेल</a></li>
                    <li><a href="#business">व्यापार</a></li>
                    <li><a href="#tech">तकनीक</a></li>
                    <li><a href="#entertainment">मनोरंजन</a></li>
                    <li><a href="#contact">संपर्क</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Modern Main Content -->
    <main id="main" class="site-content">
        <div class="container">

            <!-- Hero Section -->
            <section class="hero-section" style="margin: var(--space-2xl) 0; padding: var(--space-2xl); background: linear-gradient(135deg, var(--background-accent), var(--background-light)); border-radius: var(--radius-xl); text-align: center;">
                <h1 style="font-size: 2.5rem; color: var(--text-primary); margin-bottom: var(--space-lg);">आधुनिक हिंदी समाचार</h1>
                <p style="font-size: 1.125rem; color: var(--text-secondary); max-width: 600px; margin: 0 auto var(--space-xl) auto;">तेज़, सुलभ और विश्वसनीय समाचार अपडेट के लिए हमारे साथ जुड़ें</p>
                <button class="button" style="font-size: 1rem; padding: var(--space-lg) var(--space-2xl);">अभी पढ़ें</button>
            </section>

            <!-- Latest News Section -->
            <section class="news-section">
                <h2 class="gb-text gb-text-4666a877">ताज़ा समाचार</h2>

                <div class="gb-looper-b95cf960">
                    <!-- News Card 1 -->
                    <article class="gb-loop-item animate-fade-in-up">
                        <a href="#" class="post-link" aria-label="भारत में नई तकनीकी क्रांति का आगाज - पूरी खबर पढ़ें">
                            <img class="gb-media-97d5d870" src="https://via.placeholder.com/400x200/ff6b35/ffffff?text=तकनीकी+क्रांति" alt="भारत में नई तकनीकी क्रांति" loading="lazy" />
                        </a>
                        <div class="post-content">
                            <h3 class="gb-text gb-text-d67b07a7">
                                <a href="#" aria-label="भारत में नई तकनीकी क्रांति का आगाज - विस्तार से पढ़ें">भारत में नई तकनीकी क्रांति का आगाज</a>
                            </h3>
                            <p style="color: var(--text-light); font-size: 0.875rem; line-height: 1.6; margin-bottom: var(--space-md);">
                                आर्टिफिशियल इंटेलिजेंस और मशीन लर्निंग के क्षेत्र में भारत की नई उपलब्धियां...
                            </p>
                            <div class="gb-element-6f73e5ba">
                                <img class="gb-media-7e79f4af" src="https://via.placeholder.com/32x32/ff6b35/ffffff?text=RS" alt="राहुल शर्मा का अवतार" loading="lazy" />
                                <div class="gb-text-4f7117f3">
                                    <span class="gb-text">राहुल शर्मा</span>
                                    <span class="gb-shape">
                                        <svg viewBox="0 0 24 24" width="16" height="16" aria-label="सत्यापित लेखक">
                                            <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                        </svg>
                                    </span>
                                </div>
                                <div class="gb-text gb-text-fbe00f43">—</div>
                                <div class="gb-text gb-text-48340570">2 घंटे पहले</div>
                            </div>
                        </div>
                    </article>

                <!-- News Card 2 -->
                <article class="gb-loop-item">
                    <a href="#" class="post-link">
                        <img class="gb-media-97d5d870" src="https://via.placeholder.com/400x250/e67e22/ffffff?text=समाचार+2" alt="समाचार 2" />
                    </a>
                    <div class="post-content">
                        <h3 class="gb-text gb-text-d67b07a7">
                            <a href="#">खेल जगत में भारत की नई उपलब्धि</a>
                        </h3>
                        <div class="gb-element-6f73e5ba">
                            <img class="gb-media-7e79f4af" src="https://via.placeholder.com/25x25/e67e22/ffffff?text=P" alt="लेखक अवतार" />
                            <div class="gb-text-4f7117f3">
                                <span class="gb-text">प्रिया गुप्ता</span>
                                <span class="gb-shape">
                                    <svg viewBox="0 0 24 24" width="16" height="16">
                                        <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="gb-text gb-text-fbe00f43">—</div>
                            <div class="gb-text gb-text-48340570">4 घंटे पहले</div>
                        </div>
                    </div>
                </article>

                <!-- News Card 3 -->
                <article class="gb-loop-item">
                    <a href="#" class="post-link">
                        <img class="gb-media-97d5d870" src="https://via.placeholder.com/400x250/2c3e50/ffffff?text=समाचार+3" alt="समाचार 3" />
                    </a>
                    <div class="post-content">
                        <h3 class="gb-text gb-text-d67b07a7">
                            <a href="#">आर्थिक सुधार की नई दिशा</a>
                        </h3>
                        <div class="gb-element-6f73e5ba">
                            <img class="gb-media-7e79f4af" src="https://via.placeholder.com/25x25/2c3e50/ffffff?text=A" alt="लेखक अवतार" />
                            <div class="gb-text-4f7117f3">
                                <span class="gb-text">अमित कुमार</span>
                                <span class="gb-shape">
                                    <svg viewBox="0 0 24 24" width="16" height="16">
                                        <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="gb-text gb-text-fbe00f43">—</div>
                            <div class="gb-text gb-text-48340570">6 घंटे पहले</div>
                        </div>
                    </div>
                </article>
            </div>
        </section>

        <!-- Business News Section -->
        <section class="news-section">
            <h2 class="gb-text gb-text-54ea9bb4">व्यापार समाचार</h2>
            
            <div class="gb-looper-a0414682">
                <!-- Business Card 1 -->
                <article class="gb-loop-item">
                    <a href="#" class="post-link">
                        <img class="gb-media-8778d61a" src="https://via.placeholder.com/400x250/34495e/ffffff?text=व्यापार+1" alt="व्यापार समाचार 1" />
                    </a>
                    <div class="post-content">
                        <h3 class="gb-text gb-text-b56eacd4">
                            <a href="#">स्टार्टअप इकोसिस्टम में नया निवेश</a>
                        </h3>
                        <div class="gb-element-bcff044a">
                            <img class="gb-media-e333169e" src="https://via.placeholder.com/25x25/34495e/ffffff?text=S" alt="लेखक अवतार" />
                            <div class="gb-text-5beb8661">
                                <span class="gb-text">सुनीता वर्मा</span>
                                <span class="gb-shape">
                                    <svg viewBox="0 0 24 24" width="16" height="16">
                                        <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="gb-text gb-text-23d24cd8">—</div>
                            <div class="gb-text gb-text-c97b5596">1 दिन पहले</div>
                        </div>
                    </div>
                </article>

                <!-- Business Card 2 -->
                <article class="gb-loop-item">
                    <a href="#" class="post-link">
                        <img class="gb-media-8778d61a" src="https://via.placeholder.com/400x250/ff6b35/ffffff?text=व्यापार+2" alt="व्यापार समाचार 2" />
                    </a>
                    <div class="post-content">
                        <h3 class="gb-text gb-text-b56eacd4">
                            <a href="#">डिजिटल पेमेंट में नई क्रांति</a>
                        </h3>
                        <div class="gb-element-bcff044a">
                            <img class="gb-media-e333169e" src="https://via.placeholder.com/25x25/ff6b35/ffffff?text=V" alt="लेखक अवतार" />
                            <div class="gb-text-5beb8661">
                                <span class="gb-text">विकास सिंह</span>
                                <span class="gb-shape">
                                    <svg viewBox="0 0 24 24" width="16" height="16">
                                        <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="gb-text gb-text-23d24cd8">—</div>
                            <div class="gb-text gb-text-c97b5596">2 दिन पहले</div>
                        </div>
                    </div>
                </article>

                <!-- Business Card 3 -->
                <article class="gb-loop-item">
                    <a href="#" class="post-link">
                        <img class="gb-media-8778d61a" src="https://via.placeholder.com/400x250/e67e22/ffffff?text=व्यापार+3" alt="व्यापार समाचार 3" />
                    </a>
                    <div class="post-content">
                        <h3 class="gb-text gb-text-b56eacd4">
                            <a href="#">ई-कॉमर्स सेक्टर में तेजी</a>
                        </h3>
                        <div class="gb-element-bcff044a">
                            <img class="gb-media-e333169e" src="https://via.placeholder.com/25x25/e67e22/ffffff?text=N" alt="लेखक अवतार" />
                            <div class="gb-text-5beb8661">
                                <span class="gb-text">नेहा अग्रवाल</span>
                                <span class="gb-shape">
                                    <svg viewBox="0 0 24 24" width="16" height="16">
                                        <path d="M23,12L20.56,9.22L20.9,5.54L17.29,4.72L15.4,1.54L12,3L8.6,1.54L6.71,4.72L3.1,5.53L3.44,9.21L1,12L3.44,14.78L3.1,18.47L6.71,19.29L8.6,22.47L12,21L15.4,22.46L17.29,19.28L20.9,18.46L20.56,14.78L23,12M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"></path>
                                    </svg>
                                </span>
                            </div>
                            <div class="gb-text gb-text-23d24cd8">—</div>
                            <div class="gb-text gb-text-c97b5596">3 दिन पहले</div>
                        </div>
                    </div>
                </article>
            </div>
        </section>

        <!-- Social Media Section -->
        <section class="social-section">
            <div class="social-theme-consistent">
                <span class="social-theme-text">हमें फॉलो करें</span>
                <div class="social-theme-icons">
                    <a href="#" class="social-theme-icon social-facebook-theme" aria-label="Facebook पर फॉलो करें">📘</a>
                    <a href="#" class="social-theme-icon social-whatsapp-theme" aria-label="WhatsApp पर जुड़ें">💬</a>
                    <a href="#" class="social-theme-icon social-telegram-theme" aria-label="Telegram पर जुड़ें">✈️</a>
                    <a href="#" class="social-theme-icon social-share-theme" aria-label="शेयर करें">🔗</a>
                </div>
            </div>
        </section>

        <!-- Pagination -->
        <nav class="gb-query-loop-pagination" aria-label="Posts navigation">
            <a href="#" class="gb-text-5f6cdbaa" aria-label="Previous page">
                <span>← पिछला</span>
            </a>
            <a href="#" class="page-numbers current" aria-label="Page 1, current page">1</a>
            <a href="#" class="page-numbers" aria-label="Page 2">2</a>
            <a href="#" class="page-numbers" aria-label="Page 3">3</a>
            <a href="#" class="gb-text-6dc778f6" aria-label="Next page">
                <span>अगला →</span>
            </a>
        </nav>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <p>&copy; 2024 GP Discover Pro. सभी अधिकार सुरक्षित।</p>
            <nav class="footer-navigation">
                <a href="#privacy">गोपनीयता नीति</a>
                <a href="#terms">नियम और शर्तें</a>
                <a href="#contact">संपर्क</a>
            </nav>
        </div>
    </footer>
</body>
</html>
