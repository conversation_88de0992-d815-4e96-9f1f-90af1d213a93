/*
Theme Name: GP Discover Pro
Description: Professional GeneratePress child theme with performance optimization, SEO features, and modern design
Author: Professional WordPress Developer
Template: generatepress
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: gp-discover-pro
Tags: responsive, accessibility-ready, custom-colors, custom-menu, featured-images, threaded-comments, translation-ready
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
*/

/* ==========================================================================
   CRITICAL CSS - Above the fold styles for performance
   ========================================================================== */

/* CSS Custom Properties for consistent theming */
:root {
    /* Primary Colors */
    --primary-color: #ff6b35;
    --primary-hover: #e55a2b;
    --primary-light: #ff8c5a;
    --primary-dark: #d4541c;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #34495e;
    --text-light: #7f8c8d;
    --text-muted: #95a5a6;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;
    --bg-accent: #fef2e7;
    --bg-card: #ffffff;
    
    /* Border & Shadow */
    --border-color: #e9ecef;
    --border-light: #f1f3f4;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    
    /* Spacing System */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Noto Sans Devanagari', serif;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Container Widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ==========================================================================
   BASE STYLES - Critical above-the-fold styles
   ========================================================================== */

/* Reset and Base */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    margin: 0;
    padding: 0;
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   CRITICAL HEADER STYLES - Above the fold
   ========================================================================== */

.site-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

.site-branding {
    padding: var(--space-lg) 0;
}

.site-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.025em;
}

.main-navigation {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-light);
}

.main-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-md);
    justify-content: center;
    flex-wrap: wrap;
}

.main-navigation a {
    display: block;
    padding: var(--space-lg) var(--space-xl);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: var(--transition-fast);
    border-radius: var(--radius-md);
    text-decoration: none;
}

.main-navigation a:hover,
.main-navigation a:focus {
    color: var(--primary-color);
    background: var(--bg-accent);
}

/* ==========================================================================
   CRITICAL LAYOUT STYLES - Above the fold
   ========================================================================== */

.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--space-lg);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

/* ==========================================================================
   CRITICAL TYPOGRAPHY - Above the fold
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin: 0 0 var(--space-lg) 0;
    letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin: 0 0 var(--space-lg) 0;
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* ==========================================================================
   CRITICAL RESPONSIVE - Mobile First
   ========================================================================== */

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .container {
        padding: 0 var(--space-md);
    }
    
    .site-title {
        font-size: 1.5rem;
    }
    
    .main-navigation ul {
        flex-direction: column;
        gap: 0;
    }
    
    .main-navigation a {
        padding: var(--space-md) var(--space-lg);
        text-align: center;
        border-radius: 0;
        border-bottom: 1px solid var(--border-light);
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
    
    .grid-cols-4,
    .grid-cols-3,
    .grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* ==========================================================================
   ACCESSIBILITY - Critical for screen readers
   ========================================================================== */

.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    word-wrap: normal !important;
}

.screen-reader-text:focus {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-md);
    clip: auto !important;
    color: var(--text-primary);
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    height: auto;
    left: var(--space-sm);
    line-height: normal;
    padding: var(--space-md) var(--space-lg);
    text-decoration: none;
    top: var(--space-sm);
    width: auto;
    z-index: 100000;
}

.skip-link {
    position: absolute;
    left: -9999px;
    top: -9999px;
}

.skip-link:focus {
    left: var(--space-md);
    top: var(--space-md);
    background: var(--text-primary);
    color: var(--text-white);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    z-index: 999999;
}

/* Focus indicators */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ==========================================================================
   PERFORMANCE - Critical loading states
   ========================================================================== */

/* Loading placeholder for images */
.lazy-loading {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, transparent 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Critical resource hints */
.preload-font {
    font-display: swap;
}

/* ==========================================================================
   MOBILE-FIRST RESPONSIVE DESIGN
   ========================================================================== */

/* Touch-friendly navigation */
@media (max-width: 768px) {
    .main-navigation {
        position: relative;
    }

    .main-navigation ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        box-shadow: var(--shadow-lg);
        border-radius: 0 0 var(--radius-md) var(--radius-md);
        z-index: 1000;
    }

    .main-navigation ul.show {
        display: flex;
        flex-direction: column;
    }

    .main-navigation a {
        padding: var(--space-lg);
        border-bottom: 1px solid var(--border-light);
        min-height: 48px;
        display: flex;
        align-items: center;
        font-size: 1rem;
        text-transform: none;
        letter-spacing: normal;
    }

    .main-navigation a:hover,
    .main-navigation a:focus {
        background: var(--primary-color);
        color: var(--text-white);
    }

    /* Mobile menu toggle */
    .menu-toggle {
        display: block;
        background: none;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        padding: var(--space-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        min-height: 48px;
        min-width: 48px;
        position: relative;
    }

    .menu-toggle:hover,
    .menu-toggle:focus {
        background: var(--primary-color);
        color: var(--text-white);
    }

    .menu-toggle span {
        display: block;
        width: 20px;
        height: 2px;
        background: currentColor;
        margin: 4px 0;
        transition: var(--transition-fast);
    }

    .menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Desktop navigation (hide toggle) */
@media (min-width: 769px) {
    .menu-toggle {
        display: none;
    }

    .main-navigation ul {
        display: flex !important;
    }
}

/* ==========================================================================
   MODERN CSS GRID LAYOUTS
   ========================================================================== */

/* Post Grid */
.posts-grid {
    display: grid;
    gap: var(--space-xl);
    margin: var(--space-2xl) 0;
}

/* Responsive grid columns */
@media (min-width: 1200px) {
    .posts-grid { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .posts-grid { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 480px) and (max-width: 767px) {
    .posts-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 479px) {
    .posts-grid { grid-template-columns: 1fr; }
}

/* Card Component */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition-normal);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-image {
    aspect-ratio: 16/9;
    overflow: hidden;
    position: relative;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.card:hover .card-image img {
    transform: scale(1.05);
}

.card-content {
    padding: var(--space-lg);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

.card-title a {
    color: inherit;
    text-decoration: none;
}

.card-title a:hover {
    color: var(--primary-color);
}

.card-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
    flex-grow: 1;
}

.card-meta {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: auto;
}

.card-meta .author-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* ==========================================================================
   MODERN FLEXBOX UTILITIES
   ========================================================================== */

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* ==========================================================================
   MODERN SPACING UTILITIES
   ========================================================================== */

/* Margin utilities */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

/* Padding utilities */
.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }

/* ==========================================================================
   END OF CRITICAL CSS
   Non-critical styles will be loaded asynchronously
   ========================================================================== */
