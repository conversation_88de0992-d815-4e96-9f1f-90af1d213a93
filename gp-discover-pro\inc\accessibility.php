<?php
/**
 * Accessibility Features
 * 
 * @package GP_Discover_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Accessibility Class
 */
class GP_Discover_Pro_Accessibility {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_head', array($this, 'add_accessibility_styles'));
        add_action('wp_footer', array($this, 'add_accessibility_scripts'));
        add_filter('nav_menu_link_attributes', array($this, 'add_menu_link_attributes'), 10, 4);
        add_filter('wp_get_attachment_image_attributes', array($this, 'improve_image_accessibility'));
        add_filter('the_content', array($this, 'improve_content_accessibility'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_accessibility_scripts'));
        add_action('init', array($this, 'add_accessibility_features'));
        add_filter('wp_nav_menu_args', array($this, 'add_menu_accessibility'));
    }
    
    /**
     * Add Accessibility Styles
     */
    public function add_accessibility_styles() {
        ?>
        <style id="gp-discover-pro-accessibility-css">
        /* Skip Links */
        .skip-links {
            position: absolute;
            left: -9999px;
            top: -9999px;
            z-index: 999999;
        }
        
        .skip-links a {
            position: absolute;
            left: -9999px;
            top: -9999px;
            background: var(--text-primary, #2c3e50);
            color: var(--text-white, #ffffff);
            padding: 12px 16px;
            text-decoration: none;
            font-weight: 600;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .skip-links a:focus {
            left: 16px;
            top: 16px;
            clip: auto;
            width: auto;
            height: auto;
        }
        
        /* Enhanced Focus Indicators */
        <?php if (get_theme_mod('enhanced_focus_indicators', true)): ?>
        a:focus,
        button:focus,
        input:focus,
        textarea:focus,
        select:focus,
        [tabindex]:focus {
            outline: 3px solid var(--primary-color, #ff6b35) !important;
            outline-offset: 2px !important;
            box-shadow: 0 0 0 1px var(--bg-primary, #ffffff) !important;
        }
        
        .focus-visible {
            outline: 3px solid var(--primary-color, #ff6b35) !important;
            outline-offset: 2px !important;
        }
        <?php endif; ?>
        
        /* High Contrast Mode */
        <?php if (get_theme_mod('high_contrast_mode', false)): ?>
        @media (prefers-contrast: high) {
            :root {
                --text-primary: #000000 !important;
                --text-secondary: #000000 !important;
                --bg-primary: #ffffff !important;
                --primary-color: #0000ff !important;
                --border-color: #000000 !important;
            }
            
            * {
                background-color: inherit !important;
                color: inherit !important;
            }
            
            a {
                color: #0000ff !important;
                text-decoration: underline !important;
            }
            
            button, .button {
                background: #000000 !important;
                color: #ffffff !important;
                border: 2px solid #000000 !important;
            }
        }
        <?php endif; ?>
        
        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }
        
        /* Screen Reader Only Text */
        .sr-only {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }
        
        .sr-only:focus {
            position: static !important;
            width: auto !important;
            height: auto !important;
            padding: inherit !important;
            margin: inherit !important;
            overflow: visible !important;
            clip: auto !important;
            white-space: inherit !important;
        }
        
        /* Touch Target Improvements */
        @media (max-width: 768px) {
            a, button, input, select, textarea, [role="button"], [tabindex] {
                min-height: 44px !important;
                min-width: 44px !important;
            }
            
            .main-navigation a {
                padding: 16px 20px !important;
                display: flex !important;
                align-items: center !important;
            }
        }
        
        /* Color Contrast Improvements */
        .low-contrast-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 4px;
            margin: 16px 0;
        }
        
        /* Keyboard Navigation Improvements */
        .keyboard-navigation-active *:focus {
            outline: 3px solid var(--primary-color, #ff6b35) !important;
            outline-offset: 2px !important;
        }
        
        /* ARIA Live Regions */
        .aria-live-region {
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        }
        </style>
        <?php
    }
    
    /**
     * Add Accessibility Scripts
     */
    public function add_accessibility_scripts() {
        ?>
        <script id="gp-discover-pro-accessibility-js">
        (function() {
            'use strict';
            
            // Keyboard navigation detection
            let isKeyboardNavigation = false;
            
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    isKeyboardNavigation = true;
                    document.body.classList.add('keyboard-navigation-active');
                }
            });
            
            document.addEventListener('mousedown', function() {
                isKeyboardNavigation = false;
                document.body.classList.remove('keyboard-navigation-active');
            });
            
            // Skip link functionality
            const skipLinks = document.querySelectorAll('.skip-links a');
            skipLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.focus();
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
            
            // ARIA live region for dynamic content
            const liveRegion = document.createElement('div');
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'aria-live-region';
            document.body.appendChild(liveRegion);
            
            // Announce page changes for SPA-like behavior
            window.announceToScreenReader = function(message) {
                liveRegion.textContent = message;
                setTimeout(function() {
                    liveRegion.textContent = '';
                }, 1000);
            };
            
            // Improve form accessibility
            const forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(function(input) {
                    // Add required indicator
                    if (input.hasAttribute('required')) {
                        const label = form.querySelector('label[for="' + input.id + '"]');
                        if (label && !label.querySelector('.required-indicator')) {
                            const indicator = document.createElement('span');
                            indicator.className = 'required-indicator sr-only';
                            indicator.textContent = ' (required)';
                            label.appendChild(indicator);
                        }
                    }
                    
                    // Add ARIA describedby for error messages
                    const errorElement = form.querySelector('.error-' + input.name);
                    if (errorElement) {
                        input.setAttribute('aria-describedby', errorElement.id);
                        input.setAttribute('aria-invalid', 'true');
                    }
                });
            });
            
            // Improve image accessibility
            const images = document.querySelectorAll('img');
            images.forEach(function(img) {
                // Add role="presentation" for decorative images
                if (!img.alt || img.alt.trim() === '') {
                    img.setAttribute('role', 'presentation');
                    img.setAttribute('alt', '');
                }
                
                // Add loading announcement
                if (img.hasAttribute('loading') && img.getAttribute('loading') === 'lazy') {
                    img.addEventListener('load', function() {
                        if (isKeyboardNavigation) {
                            announceToScreenReader('Image loaded: ' + (this.alt || 'Decorative image'));
                        }
                    });
                }
            });
            
            // Improve navigation accessibility
            const navMenus = document.querySelectorAll('.main-navigation ul');
            navMenus.forEach(function(menu) {
                menu.setAttribute('role', 'menubar');
                
                const menuItems = menu.querySelectorAll('li');
                menuItems.forEach(function(item, index) {
                    const link = item.querySelector('a');
                    if (link) {
                        link.setAttribute('role', 'menuitem');
                        link.setAttribute('tabindex', index === 0 ? '0' : '-1');
                    }
                    
                    // Handle submenus
                    const submenu = item.querySelector('ul');
                    if (submenu) {
                        submenu.setAttribute('role', 'menu');
                        submenu.setAttribute('aria-hidden', 'true');
                        
                        link.setAttribute('aria-haspopup', 'true');
                        link.setAttribute('aria-expanded', 'false');
                        
                        // Keyboard navigation for submenus
                        link.addEventListener('keydown', function(e) {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                                this.setAttribute('aria-expanded', !isExpanded);
                                submenu.setAttribute('aria-hidden', isExpanded);
                            }
                        });
                    }
                });
            });
            
            // Color contrast checker
            function checkColorContrast() {
                const elements = document.querySelectorAll('*');
                elements.forEach(function(element) {
                    const styles = window.getComputedStyle(element);
                    const backgroundColor = styles.backgroundColor;
                    const color = styles.color;
                    
                    // Simple contrast check (would need more sophisticated implementation)
                    if (backgroundColor !== 'rgba(0, 0, 0, 0)' && color !== 'rgba(0, 0, 0, 0)') {
                        // This is a simplified check - in production, you'd use a proper contrast ratio calculation
                        const bgLuminance = getLuminance(backgroundColor);
                        const textLuminance = getLuminance(color);
                        const contrast = (Math.max(bgLuminance, textLuminance) + 0.05) / (Math.min(bgLuminance, textLuminance) + 0.05);
                        
                        if (contrast < 4.5) {
                            console.warn('Low contrast detected:', element, 'Contrast ratio:', contrast);
                        }
                    }
                });
            }
            
            function getLuminance(color) {
                // Simplified luminance calculation
                // In production, you'd use a proper color parsing library
                return 0.5; // Placeholder
            }
            
            // Run contrast check in development mode
            if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
                setTimeout(checkColorContrast, 1000);
            }
            
            // Announce route changes for SPA-like behavior
            let currentPath = window.location.pathname;
            const observer = new MutationObserver(function(mutations) {
                if (window.location.pathname !== currentPath) {
                    currentPath = window.location.pathname;
                    announceToScreenReader('Page changed to ' + document.title);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Improve table accessibility
            const tables = document.querySelectorAll('table');
            tables.forEach(function(table) {
                if (!table.querySelector('caption')) {
                    const caption = document.createElement('caption');
                    caption.className = 'sr-only';
                    caption.textContent = 'Data table';
                    table.insertBefore(caption, table.firstChild);
                }
                
                // Add scope attributes to headers
                const headers = table.querySelectorAll('th');
                headers.forEach(function(header) {
                    if (!header.hasAttribute('scope')) {
                        const isInThead = header.closest('thead');
                        header.setAttribute('scope', isInThead ? 'col' : 'row');
                    }
                });
            });
            
        })();
        </script>
        <?php
    }
    
    /**
     * Enqueue Accessibility Scripts
     */
    public function enqueue_accessibility_scripts() {
        wp_enqueue_script(
            'gp-discover-pro-accessibility',
            GP_DISCOVER_PRO_URL . '/assets/js/accessibility.min.js',
            array('jquery'),
            GP_DISCOVER_PRO_VERSION,
            true
        );
        
        wp_localize_script('gp-discover-pro-accessibility', 'gpAccessibility', array(
            'skipToContent' => __('Skip to main content', 'gp-discover-pro'),
            'skipToNavigation' => __('Skip to navigation', 'gp-discover-pro'),
            'menuExpanded' => __('Menu expanded', 'gp-discover-pro'),
            'menuCollapsed' => __('Menu collapsed', 'gp-discover-pro'),
            'loading' => __('Loading...', 'gp-discover-pro'),
            'loaded' => __('Content loaded', 'gp-discover-pro'),
        ));
    }
    
    /**
     * Add Menu Link Attributes
     */
    public function add_menu_link_attributes($atts, $item, $args, $depth) {
        // Add ARIA attributes for menu items
        if (in_array('menu-item-has-children', $item->classes)) {
            $atts['aria-haspopup'] = 'true';
            $atts['aria-expanded'] = 'false';
        }
        
        // Add descriptive text for external links
        if (strpos($atts['href'], home_url()) === false && strpos($atts['href'], 'http') === 0) {
            $atts['aria-label'] = $item->title . ' ' . __('(opens in new window)', 'gp-discover-pro');
            $atts['target'] = '_blank';
            $atts['rel'] = 'noopener noreferrer';
        }
        
        return $atts;
    }
    
    /**
     * Improve Image Accessibility
     */
    public function improve_image_accessibility($attr) {
        // Ensure all images have alt attributes
        if (!isset($attr['alt'])) {
            $attr['alt'] = '';
        }
        
        // Add role="presentation" for decorative images
        if (empty($attr['alt'])) {
            $attr['role'] = 'presentation';
        }
        
        // Add loading and decoding attributes
        $attr['loading'] = 'lazy';
        $attr['decoding'] = 'async';
        
        return $attr;
    }
    
    /**
     * Improve Content Accessibility
     */
    public function improve_content_accessibility($content) {
        // Add skip links if enabled
        if (get_theme_mod('enable_skip_links', true)) {
            $skip_links = '<div class="skip-links">';
            $skip_links .= '<a href="#main" class="skip-link">' . __('Skip to main content', 'gp-discover-pro') . '</a>';
            $skip_links .= '<a href="#navigation" class="skip-link">' . __('Skip to navigation', 'gp-discover-pro') . '</a>';
            $skip_links .= '</div>';
            
            $content = $skip_links . $content;
        }
        
        // Improve heading structure
        $content = $this->improve_heading_structure($content);
        
        // Add ARIA labels to links without descriptive text
        $content = preg_replace_callback('/<a([^>]*?)>(.*?)<\/a>/i', function($matches) {
            $link_content = strip_tags($matches[2]);
            $link_attrs = $matches[1];
            
            // Check for non-descriptive link text
            $non_descriptive = array('click here', 'read more', 'more', 'here', 'link');
            if (in_array(strtolower(trim($link_content)), $non_descriptive)) {
                // Try to find context from surrounding content
                // This is a simplified implementation
                if (!strpos($link_attrs, 'aria-label')) {
                    $link_attrs .= ' aria-label="' . esc_attr($link_content . ' - needs descriptive text') . '"';
                }
            }
            
            return '<a' . $link_attrs . '>' . $matches[2] . '</a>';
        }, $content);
        
        return $content;
    }
    
    /**
     * Improve Heading Structure
     */
    private function improve_heading_structure($content) {
        // This is a simplified implementation
        // In production, you'd want more sophisticated heading level management
        
        $headings = array();
        preg_match_all('/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/i', $content, $matches, PREG_SET_ORDER);
        
        foreach ($matches as $match) {
            $level = intval($match[1]);
            $text = strip_tags($match[2]);
            
            // Add to heading structure for potential TOC generation
            $headings[] = array(
                'level' => $level,
                'text' => $text,
                'id' => sanitize_title($text)
            );
        }
        
        // Store headings for potential use in TOC
        if (!empty($headings)) {
            global $gp_discover_pro_headings;
            $gp_discover_pro_headings = $headings;
        }
        
        return $content;
    }
    
    /**
     * Add Accessibility Features
     */
    public function add_accessibility_features() {
        // Add landmark roles
        add_action('gp_discover_pro_header', function() {
            echo '<header role="banner">';
        }, 1);
        
        add_action('gp_discover_pro_header', function() {
            echo '</header>';
        }, 999);
        
        add_action('gp_discover_pro_navigation', function() {
            echo '<nav role="navigation" aria-label="' . esc_attr__('Main Navigation', 'gp-discover-pro') . '">';
        }, 1);
        
        add_action('gp_discover_pro_navigation', function() {
            echo '</nav>';
        }, 999);
        
        add_action('gp_discover_pro_main', function() {
            echo '<main role="main" id="main">';
        }, 1);
        
        add_action('gp_discover_pro_main', function() {
            echo '</main>';
        }, 999);
        
        add_action('gp_discover_pro_footer', function() {
            echo '<footer role="contentinfo">';
        }, 1);
        
        add_action('gp_discover_pro_footer', function() {
            echo '</footer>';
        }, 999);
    }
    
    /**
     * Add Menu Accessibility
     */
    public function add_menu_accessibility($args) {
        if ($args['theme_location'] === 'primary') {
            $args['container_id'] = 'navigation';
            $args['menu_id'] = 'primary-menu';
            $args['menu_class'] = 'primary-menu';
        }
        
        return $args;
    }
}

// Initialize Accessibility
new GP_Discover_Pro_Accessibility();
